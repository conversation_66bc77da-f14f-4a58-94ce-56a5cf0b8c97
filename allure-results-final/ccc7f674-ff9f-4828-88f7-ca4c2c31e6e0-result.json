{"name": "不同数字的加法运算 -- @1.1 ", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750938559195, "stop": 1750938559195}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750938559195, "stop": 1750938559195}, {"name": "And 我已经输入数字 20 到计算器中", "status": "skipped", "start": 1750938559195, "stop": 1750938559195}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750938559195, "stop": 1750938559195}, {"name": "Then 屏幕上应该显示结果 30", "status": "skipped", "start": 1750938559196, "stop": 1750938559196}], "parameters": [{"name": "第一个数", "value": "10"}, {"name": "第二个数", "value": "20"}, {"name": "结果", "value": "30"}], "start": 1750938559194, "stop": 1750938559196, "uuid": "300c26f9-a14a-4cc2-850e-3295e07dec45", "historyId": "d13f8d4ff0120894e90927ccbaa9a3df", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}