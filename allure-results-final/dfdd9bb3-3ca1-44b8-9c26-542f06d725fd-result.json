{"name": "计算幂运算", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750938559212, "stop": 1750938559212}, {"name": "Given 我已经输入数字 2 到计算器中", "status": "skipped", "start": 1750938559212, "stop": 1750938559212}, {"name": "And 我已经输入数字 3 到计算器中", "status": "skipped", "start": 1750938559212, "stop": 1750938559212}, {"name": "When 我按下幂运算按钮", "status": "skipped", "start": 1750938559212, "stop": 1750938559212}, {"name": "Then 屏幕上应该显示结果 8", "status": "skipped", "start": 1750938559212, "stop": 1750938559212}], "start": 1750938559210, "stop": 1750938559212, "uuid": "fefd8ed3-c324-4605-ad99-275d30e5b969", "historyId": "c68aa1364bc6dcc719a82e99b799f3b0", "fullName": "计算器功能: 计算幂运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}