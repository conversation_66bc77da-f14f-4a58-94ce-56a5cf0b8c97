{"name": "测试报告书写", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "8a515cd6-4c9a-4271-ba36-5711ac7059d6-attachment.txt", "type": "text/plain"}], "start": 1750938559217, "stop": 1750938559219}, {"name": "Given given: 测试111", "status": "passed", "start": 1750938559219, "stop": 1750938559221}, {"name": "When when: 测试111", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "start": 1750938559221, "stop": 1750938559226}, {"name": "Then then: 测试111", "status": "skipped", "start": 1750938559227, "stop": 1750938559227}], "attachments": [{"name": "Scenario Logs - 测试报告书写", "source": "b2eb8755-70fb-42c8-8585-acfeac42dea4-attachment.txt", "type": "text/plain"}], "start": 1750938559215, "stop": 1750938559227, "uuid": "e640ec63-6ef3-43bf-a45e-c909f1ef3870", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}