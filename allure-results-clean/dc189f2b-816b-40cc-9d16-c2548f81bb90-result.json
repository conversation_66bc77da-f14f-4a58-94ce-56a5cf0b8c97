{"name": "测试报告书写", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "2c3dbb76-76be-4d7c-ae0c-40278669723a-attachment.txt", "type": "text/plain"}], "start": 1750991285434, "stop": 1750991285435}, {"name": "Given given: 测试111", "status": "passed", "start": 1750991285436, "stop": 1750991285438}, {"name": "When when: 测试111", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "start": 1750991285438, "stop": 1750991285442}, {"name": "Then then: 测试111", "status": "skipped", "start": 1750991285443, "stop": 1750991285443}], "attachments": [{"name": "Scenario Logs - 测试报告书写", "source": "82001af3-5544-4576-82c0-e4eb3f090cff-attachment.txt", "type": "text/plain"}], "start": 1750991285432, "stop": 1750991285443, "uuid": "296e3d46-a932-47f2-8b7f-18728768dd88", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}