{"name": "两个数字相乘", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750991285417, "stop": 1750991285417}, {"name": "Given 我已经输入数字 6 到计算器中", "status": "skipped", "start": 1750991285417, "stop": 1750991285417}, {"name": "And 我已经输入数字 7 到计算器中", "status": "skipped", "start": 1750991285417, "stop": 1750991285417}, {"name": "When 我按下乘法按钮", "status": "skipped", "start": 1750991285417, "stop": 1750991285417}, {"name": "Then 屏幕上应该显示结果 42", "status": "skipped", "start": 1750991285417, "stop": 1750991285417}], "start": 1750991285416, "stop": 1750991285417, "uuid": "c8d80cbd-c7b2-4eeb-95e7-9fff6ef5d7c5", "historyId": "f3662b2d33db3b7b0965a67f9fdce563", "fullName": "计算器功能: 两个数字相乘", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}