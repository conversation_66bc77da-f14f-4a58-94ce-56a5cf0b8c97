{"name": "两个数字相减", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750991285414, "stop": 1750991285414}, {"name": "Given 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750991285415, "stop": 1750991285415}, {"name": "And 我已经输入数字 30 到计算器中", "status": "skipped", "start": 1750991285415, "stop": 1750991285415}, {"name": "When 我按下减法按钮", "status": "skipped", "start": 1750991285415, "stop": 1750991285415}, {"name": "Then 屏幕上应该显示结果 70", "status": "skipped", "start": 1750991285415, "stop": 1750991285415}], "start": 1750991285414, "stop": 1750991285415, "uuid": "ec17fda8-c421-4e54-a05b-f621492d4f7f", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "fullName": "计算器功能: 两个数字相减", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}