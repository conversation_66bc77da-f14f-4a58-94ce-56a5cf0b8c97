{"name": "两个数字相除", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750991285419, "stop": 1750991285419}, {"name": "Given 我已经输入数字 84 到计算器中", "status": "skipped", "start": 1750991285419, "stop": 1750991285419}, {"name": "And 我已经输入数字 12 到计算器中", "status": "skipped", "start": 1750991285419, "stop": 1750991285419}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750991285419, "stop": 1750991285420}, {"name": "Then 屏幕上应该显示结果 7", "status": "skipped", "start": 1750991285420, "stop": 1750991285420}], "start": 1750991285418, "stop": 1750991285420, "uuid": "d370253c-6bca-4f6f-b7bd-e9d9c65b8527", "historyId": "b173ac1221cc8eeeace98d836653d440", "fullName": "计算器功能: 两个数字相除", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}