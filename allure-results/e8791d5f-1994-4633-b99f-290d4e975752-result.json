{"name": "计算器维护计算历史记录", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "3758d2bc-b66e-4234-b1d3-677422fe8adf-attachment.txt", "type": "text/plain"}], "start": 1750918797933, "stop": 1750918797936}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "268d22b7-7b7c-4da1-a951-d695cc6afeef-attachment.txt", "type": "text/plain"}], "start": 1750918797937, "stop": 1750918797938}, {"name": "And 我已经输入数字 5 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "cc2823a9-428d-4497-a0a6-941f66d331fe-attachment.txt", "type": "text/plain"}], "start": 1750918797939, "stop": 1750918797941}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "7a2a7c65-694c-423b-a8a1-1562891392b8-attachment.txt", "type": "text/plain"}], "start": 1750918797943, "stop": 1750918797945}], "start": 1750918797942, "stop": 1750918797945}, {"name": "Then 屏幕上应该显示结果 15", "status": "passed", "steps": [{"name": "Verifying result equals 15", "status": "passed", "attachments": [{"name": "Result Verification", "source": "ab601833-c4b1-4f49-8653-f60f73f6fc4d-attachment.txt", "type": "text/plain"}], "start": 1750918797946, "stop": 1750918797947}], "start": 1750918797946, "stop": 1750918797947}, {"name": "And 计算历史记录应该包含 \"10 + 5 = 15\"", "status": "passed", "steps": [{"name": "Verifying history contains: 10 + 5 = 15", "status": "passed", "attachments": [{"name": "History Verification", "source": "c6ba701e-e69c-4138-af02-85ca0df8dfe0-attachment.txt", "type": "text/plain"}], "start": 1750918797948, "stop": 1750918797949}], "start": 1750918797948, "stop": 1750918797949}, {"name": "When 我已经输入数字 3 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "358940dc-0d5a-4198-b51e-047a675f9bfa-attachment.txt", "type": "text/plain"}], "start": 1750918797951, "stop": 1750918797953}, {"name": "And 我按下乘法按钮", "status": "passed", "steps": [{"name": "Performing multiplication", "status": "passed", "attachments": [{"name": "Operation", "source": "c9b6956d-b95c-42c1-ad1a-7f648705a5b0-attachment.txt", "type": "text/plain"}], "start": 1750918797954, "stop": 1750918797954}], "start": 1750918797953, "stop": 1750918797954}, {"name": "Then 屏幕上应该显示结果 45", "status": "passed", "steps": [{"name": "Verifying result equals 45", "status": "passed", "attachments": [{"name": "Result Verification", "source": "fbc20161-8433-432b-bed1-b7c7ea68b4eb-attachment.txt", "type": "text/plain"}], "start": 1750918797955, "stop": 1750918797956}], "start": 1750918797955, "stop": 1750918797956}, {"name": "And 计算历史记录应该包含 \"15 * 3 = 45\"", "status": "passed", "steps": [{"name": "Verifying history contains: 15 * 3 = 45", "status": "passed", "attachments": [{"name": "History Verification", "source": "b35032fb-f5cc-4dc4-8e8d-b39714240a15-attachment.txt", "type": "text/plain"}], "start": 1750918797957, "stop": 1750918797958}], "start": 1750918797957, "stop": 1750918797958}], "start": 1750918797931, "stop": 1750918797958, "uuid": "1d0a4452-042b-43ad-93ee-f1465758b8d7", "historyId": "2212440e47c6241ce16f9a6ef923a25b", "fullName": "计算器功能: 计算器维护计算历史记录", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}