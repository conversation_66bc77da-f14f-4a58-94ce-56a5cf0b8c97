{"name": "测试报告书写", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "f8ec3c96-2d72-4de3-860f-0eca414af9ad-attachment.txt", "type": "text/plain"}], "start": 1750921422277, "stop": 1750921422279}, {"name": "Given given: 测试111", "status": "passed", "start": 1750921422279, "stop": 1750921422280}, {"name": "When when: 测试111", "status": "passed", "start": 1750921422281, "stop": 1750921422283}, {"name": "Then then: 测试111", "status": "passed", "start": 1750921422284, "stop": 1750921422295}], "start": 1750921422274, "stop": 1750921422295, "uuid": "d0ce1a43-ef9c-4073-a504-5a69c56b361b", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}