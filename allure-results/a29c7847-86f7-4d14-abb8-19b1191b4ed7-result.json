{"name": "Calculating power", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "c1c821ee-b95f-4208-85df-c044d99aa863-attachment.txt", "type": "text/plain"}], "start": 1750910677406, "stop": 1750910677407}, {"name": "Given I have entered 2 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "bad7e11f-6a19-426f-add8-971930d3d413-attachment.txt", "type": "text/plain"}], "start": 1750910677407, "stop": 1750910677408}, {"name": "And I have entered 3 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "6876f663-c8b5-4ac3-a23d-b2c42c48c6ed-attachment.txt", "type": "text/plain"}], "start": 1750910677408, "stop": 1750910677409}, {"name": "When I press power", "status": "passed", "steps": [{"name": "Performing power calculation", "status": "passed", "attachments": [{"name": "Operation", "source": "5df63714-cab0-47b9-8648-9a83723fe688-attachment.txt", "type": "text/plain"}], "start": 1750910677410, "stop": 1750910677410}], "start": 1750910677409, "stop": 1750910677411}, {"name": "Then the result should be 8 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 8", "status": "passed", "attachments": [{"name": "Result Verification", "source": "70c66227-**************-35424b147f76-attachment.txt", "type": "text/plain"}], "start": 1750910677411, "stop": 1750910677412}], "start": 1750910677411, "stop": 1750910677412}], "start": 1750910677405, "stop": 1750910677412, "uuid": "ca85bf3e-37cf-4177-acbe-99d12a05658d", "historyId": "1bc50e1c959a567c5fd3b555837e554a", "fullName": "Calculator Operations: Calculating power", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}