{"name": "测试报告书写", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "fbef872a-fe18-4952-a995-165358638a86-attachment.txt", "type": "text/plain"}], "start": 1750921248645, "stop": 1750921248646}, {"name": "Given given: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "4106c529-55a9-4ce2-92e0-f54024bcaa56-attachment.txt", "type": "text/plain"}], "start": 1750921248647, "stop": 1750921248648}, {"name": "When when: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "09049261-a63f-4fe2-96f8-0939f6c61524-attachment.txt", "type": "text/plain"}], "start": 1750921248649, "stop": 1750921248650}, {"name": "Then then: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "4ad7ff3a-c6db-4841-9639-fe842aa89c0e-attachment.txt", "type": "text/plain"}], "start": 1750921248651, "stop": 1750921248652}], "start": 1750921248644, "stop": 1750921248652, "uuid": "4d209d39-c250-4144-b5d8-087ed618ca24", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}