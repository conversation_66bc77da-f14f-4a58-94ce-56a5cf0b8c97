{"name": "两个正数相加", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "70f332ac-b0a2-40eb-8f06-1bb73153d119-attachment.txt", "type": "text/plain"}], "start": 1750937711782, "stop": 1750937711784}, {"name": "Given 我已经输入数字 50 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "6c58698f-fbaa-43c4-85af-247d8d989413-attachment.txt", "type": "text/plain"}], "start": 1750937711784, "stop": 1750937711785}, {"name": "And 我已经输入数字 70 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "6a9c0e51-9d6b-4bcf-adf7-b3cd601d339e-attachment.txt", "type": "text/plain"}], "start": 1750937711785, "stop": 1750937711786}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "489aeb03-fe52-492d-a2d9-412ddaff37fa-attachment.txt", "type": "text/plain"}], "start": 1750937711786, "stop": 1750937711787}], "start": 1750937711786, "stop": 1750937711787}, {"name": "Then 屏幕上应该显示结果 120", "status": "passed", "steps": [{"name": "Verifying result equals 120", "status": "passed", "attachments": [{"name": "Result Verification", "source": "8095003d-4fae-4061-b44c-884f81ff7c0d-attachment.txt", "type": "text/plain"}], "start": 1750937711787, "stop": 1750937711788}], "start": 1750937711787, "stop": 1750937711788}], "start": 1750937711781, "stop": 1750937711788, "uuid": "66fba976-ac52-4110-9cec-0cb91584a294", "historyId": "bb528dd71bbfcb703aa589df11393955", "fullName": "计算器功能: 两个正数相加", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}