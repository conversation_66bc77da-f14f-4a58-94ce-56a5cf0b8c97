{"name": "Calculator maintains calculation history", "status": "broken", "statusDetails": {"message": "\nYou can implement step definitions for undefined steps with these snippets:\n\n@when(u'I have entered 3 into the calculator')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I have entered 3 into the calculator')\n\n"}, "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "7401868f-d5a5-48c3-8af1-31308e7da9b0-attachment.txt", "type": "text/plain"}], "start": 1750910677415, "stop": 1750910677416}, {"name": "Given I have entered 10 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "f8524422-17ae-46c2-9fc8-f0dc1e164641-attachment.txt", "type": "text/plain"}], "start": 1750910677416, "stop": 1750910677417}, {"name": "And I have entered 5 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "2dc47c7e-ef76-4bf0-9bde-d5574330a6de-attachment.txt", "type": "text/plain"}], "start": 1750910677417, "stop": 1750910677417}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "fdf74bb7-06dd-4771-ad47-8338d703dd3c-attachment.txt", "type": "text/plain"}], "start": 1750910677418, "stop": 1750910677418}], "start": 1750910677418, "stop": 1750910677418}, {"name": "Then the result should be 15 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 15", "status": "passed", "attachments": [{"name": "Result Verification", "source": "40200c1e-5f64-4a15-b053-721608abab49-attachment.txt", "type": "text/plain"}], "start": 1750910677419, "stop": 1750910677419}], "start": 1750910677419, "stop": 1750910677419}, {"name": "And the calculation history should contain \"10 + 5 = 15\"", "status": "passed", "steps": [{"name": "Verifying history contains: 10 + 5 = 15", "status": "passed", "attachments": [{"name": "History Verification", "source": "2713dadb-5cb8-421d-b774-2153867a00a9-attachment.txt", "type": "text/plain"}], "start": 1750910677420, "stop": 1750910677420}], "start": 1750910677419, "stop": 1750910677420}, {"name": "When I have entered 3 into the calculator", "status": "broken", "statusDetails": {"message": "\nYou can implement step definitions for undefined steps with these snippets:\n\n@when(u'I have entered 3 into the calculator')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I have entered 3 into the calculator')\n\n"}, "start": 1750910677420, "stop": 1750910677420}, {"name": "And I press multiply", "status": "skipped", "start": 1750910677421, "stop": 1750910677421}, {"name": "Then the result should be 45 on the screen", "status": "skipped", "start": 1750910677421, "stop": 1750910677421}, {"name": "And the calculation history should contain \"15 * 3 = 45\"", "status": "skipped", "start": 1750910677421, "stop": 1750910677421}], "start": 1750910677413, "stop": 1750910677421, "uuid": "9e82464d-fc94-4531-9bdf-f441ac212a85", "historyId": "7605ba6a4d6e760087e578640b4a5746", "fullName": "Calculator Operations: Calculator maintains calculation history", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}