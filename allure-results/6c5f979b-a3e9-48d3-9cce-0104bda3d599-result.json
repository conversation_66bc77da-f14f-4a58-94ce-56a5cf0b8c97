{"name": "两个数字相减", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "3824d6ff-a159-418d-911a-725596459afd-attachment.txt", "type": "text/plain"}], "start": 1750918797853, "stop": 1750918797856}, {"name": "Given 我已经输入数字 100 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "55610bfa-bdcd-434d-af5a-676dd125946f-attachment.txt", "type": "text/plain"}], "start": 1750918797856, "stop": 1750918797858}, {"name": "And 我已经输入数字 30 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "85b2c93c-4661-48b5-a240-01c650251ec9-attachment.txt", "type": "text/plain"}], "start": 1750918797858, "stop": 1750918797859}, {"name": "When 我按下减法按钮", "status": "passed", "steps": [{"name": "Performing subtraction", "status": "passed", "attachments": [{"name": "Operation", "source": "8abc3282-8aa3-4202-80cc-7e2df94cb635-attachment.txt", "type": "text/plain"}], "start": 1750918797860, "stop": 1750918797860}], "start": 1750918797859, "stop": 1750918797860}, {"name": "Then 屏幕上应该显示结果 70", "status": "passed", "steps": [{"name": "Verifying result equals 70", "status": "passed", "attachments": [{"name": "Result Verification", "source": "52e883fa-a3da-48cd-a25b-35e3e72e85fa-attachment.txt", "type": "text/plain"}], "start": 1750918797861, "stop": 1750918797862}], "start": 1750918797861, "stop": 1750918797862}], "start": 1750918797851, "stop": 1750918797862, "uuid": "a0134f1f-6f24-4f13-acae-f49cbc57b2e1", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "fullName": "计算器功能: 两个数字相减", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}