{"name": "两个正数相加", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750920588914, "stop": 1750920588914}, {"name": "Given 我已经输入数字 50 到计算器中", "status": "skipped", "start": 1750920588914, "stop": 1750920588914}, {"name": "And 我已经输入数字 70 到计算器中", "status": "skipped", "start": 1750920588914, "stop": 1750920588914}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750920588914, "stop": 1750920588914}, {"name": "Then 屏幕上应该显示结果 120", "status": "skipped", "start": 1750920588914, "stop": 1750920588914}], "start": 1750920588912, "stop": 1750920588914, "uuid": "32a3d363-4117-44f8-b06c-b67b0118308b", "historyId": "bb528dd71bbfcb703aa589df11393955", "fullName": "计算器功能: 两个正数相加", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}