{"name": "除零应该抛出错误", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750921317474, "stop": 1750921317474}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750921317474, "stop": 1750921317474}, {"name": "And 我已经输入数字 0 到计算器中", "status": "skipped", "start": 1750921317474, "stop": 1750921317474}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750921317474, "stop": 1750921317474}, {"name": "Then 我应该看到错误信息 \"Cannot divide by zero\"", "status": "skipped", "start": 1750921317474, "stop": 1750921317474}], "start": 1750921317473, "stop": 1750921317474, "uuid": "53f0c057-19db-4981-998e-5cf85d40c13a", "historyId": "038434e4c66ac0b41d9341671e2a4539", "fullName": "计算器功能: 除零应该抛出错误", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}