{"name": "两个数字相减", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750921422259, "stop": 1750921422259}, {"name": "Given 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750921422259, "stop": 1750921422259}, {"name": "And 我已经输入数字 30 到计算器中", "status": "skipped", "start": 1750921422259, "stop": 1750921422259}, {"name": "When 我按下减法按钮", "status": "skipped", "start": 1750921422259, "stop": 1750921422259}, {"name": "Then 屏幕上应该显示结果 70", "status": "skipped", "start": 1750921422259, "stop": 1750921422259}], "start": 1750921422258, "stop": 1750921422259, "uuid": "2d7cc742-1153-4261-a638-01ce1780f1f2", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "fullName": "计算器功能: 两个数字相减", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}