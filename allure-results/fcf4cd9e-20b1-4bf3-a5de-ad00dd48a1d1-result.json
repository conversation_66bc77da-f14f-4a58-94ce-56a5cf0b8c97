{"name": "Adding different numbers -- @1.1 ", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "b53c7bd1-bc8e-48e1-b552-1e6823c39568-attachment.txt", "type": "text/plain"}], "start": 1750910677343, "stop": 1750910677343}, {"name": "Given I have entered 10 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "c4adf7ec-dd70-445a-a5e6-4d08bb2242d2-attachment.txt", "type": "text/plain"}], "start": 1750910677343, "stop": 1750910677344}, {"name": "And I have entered 20 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "b2da8428-9093-40c0-868c-a5b0822e5de1-attachment.txt", "type": "text/plain"}], "start": 1750910677344, "stop": 1750910677345}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "218b287d-8c42-4360-85a5-c2d566211008-attachment.txt", "type": "text/plain"}], "start": 1750910677345, "stop": 1750910677345}], "start": 1750910677345, "stop": 1750910677345}, {"name": "Then the result should be 30 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 30", "status": "passed", "attachments": [{"name": "Result Verification", "source": "3879ebb1-e703-456c-ae15-cf8cc7460ed9-attachment.txt", "type": "text/plain"}], "start": 1750910677346, "stop": 1750910677346}], "start": 1750910677346, "stop": 1750910677347}], "parameters": [{"name": "first", "value": "10"}, {"name": "second", "value": "20"}, {"name": "result", "value": "30"}], "start": 1750910677341, "stop": 1750910677347, "uuid": "edfaf5c1-f47b-40f8-b158-249047a9ced8", "historyId": "6a60c83de3fb0bee4a30f60694eec811", "fullName": "Calculator Operations: Adding different numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}