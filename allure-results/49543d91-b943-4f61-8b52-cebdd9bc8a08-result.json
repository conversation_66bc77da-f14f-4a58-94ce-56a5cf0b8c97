{"name": "Dividing by zero should raise an error", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "a254af4b-f594-4026-978e-2729b121225d-attachment.txt", "type": "text/plain"}], "start": 1750910677400, "stop": 1750910677401}, {"name": "Given I have entered 10 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "de9826c0-da3a-4186-9f21-7376ebca18bb-attachment.txt", "type": "text/plain"}], "start": 1750910677401, "stop": 1750910677401}, {"name": "And I have entered 0 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "ee4f7b19-0084-4c2f-9d1b-9b21b05ebb4f-attachment.txt", "type": "text/plain"}], "start": 1750910677402, "stop": 1750910677402}, {"name": "When I press divide", "status": "passed", "steps": [{"name": "Performing division", "status": "passed", "attachments": [{"name": "Error", "source": "c5af5ed2-1b8b-4b4e-b407-8804620458a4-attachment.txt", "type": "text/plain"}], "start": 1750910677403, "stop": 1750910677403}], "start": 1750910677403, "stop": 1750910677403}, {"name": "Then I should see an error message \"Cannot divide by zero\"", "status": "passed", "steps": [{"name": "Verifying error message: Cannot divide by zero", "status": "passed", "attachments": [{"name": "Error Verification", "source": "c457cd57-2339-4690-a889-76f8810f2f10-attachment.txt", "type": "text/plain"}], "start": 1750910677403, "stop": 1750910677404}], "start": 1750910677403, "stop": 1750910677404}], "start": 1750910677399, "stop": 1750910677404, "uuid": "a57d37c2-c3c7-49cc-a071-f9ace42d0151", "historyId": "cdaec262e72d3d3dd03fb3d987cc67cc", "fullName": "Calculator Operations: Dividing by zero should raise an error", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}