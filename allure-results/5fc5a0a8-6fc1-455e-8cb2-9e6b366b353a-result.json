{"name": "两个数字相减", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750920588924, "stop": 1750920588924}, {"name": "Given 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750920588924, "stop": 1750920588924}, {"name": "And 我已经输入数字 30 到计算器中", "status": "skipped", "start": 1750920588924, "stop": 1750920588924}, {"name": "When 我按下减法按钮", "status": "skipped", "start": 1750920588924, "stop": 1750920588924}, {"name": "Then 屏幕上应该显示结果 70", "status": "skipped", "start": 1750920588924, "stop": 1750920588924}], "start": 1750920588923, "stop": 1750920588924, "uuid": "686daf50-7b54-477f-bfa9-91f14439c43b", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "fullName": "计算器功能: 两个数字相减", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}