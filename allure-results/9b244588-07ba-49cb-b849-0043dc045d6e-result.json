{"name": "Adding different numbers -- @1.2 ", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "38fbd4ad-795e-4eca-bd49-9cf6d4824df6-attachment.txt", "type": "text/plain"}], "start": 1750910440582, "stop": 1750910440583}, {"name": "Given I have entered 5 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "358468ae-7b33-4ed2-998b-8e6894dd6715-attachment.txt", "type": "text/plain"}], "start": 1750910440583, "stop": 1750910440584}, {"name": "And I have entered 15 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "2089b72a-5a42-49e6-9597-548391f3d4b1-attachment.txt", "type": "text/plain"}], "start": 1750910440585, "stop": 1750910440586}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "22769278-5bb9-4e59-ab6b-8982f9116253-attachment.txt", "type": "text/plain"}], "start": 1750910440587, "stop": 1750910440588}], "start": 1750910440587, "stop": 1750910440588}, {"name": "Then the result should be 20 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 20", "status": "passed", "attachments": [{"name": "Result Verification", "source": "118e1859-56bc-4db4-b635-75de010782bb-attachment.txt", "type": "text/plain"}], "start": 1750910440589, "stop": 1750910440589}], "start": 1750910440588, "stop": 1750910440589}], "parameters": [{"name": "first", "value": "5"}, {"name": "second", "value": "15"}, {"name": "result", "value": "20"}], "start": 1750910440580, "stop": 1750910440589, "uuid": "199fb954-667d-40e8-a727-4c98a9688322", "historyId": "e9366fe59f8c428b5a48f708dab6a760", "fullName": "Calculator Operations: Adding different numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}