{"name": "不同数字的加法运算 -- @1.2 ", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750991492299, "stop": 1750991492299}, {"name": "Given 我已经输入数字 5 到计算器中", "status": "skipped", "start": 1750991492299, "stop": 1750991492299}, {"name": "And 我已经输入数字 15 到计算器中", "status": "skipped", "start": 1750991492299, "stop": 1750991492299}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750991492299, "stop": 1750991492299}, {"name": "Then 屏幕上应该显示结果 20", "status": "skipped", "start": 1750991492299, "stop": 1750991492299}], "parameters": [{"name": "第一个数", "value": "5"}, {"name": "第二个数", "value": "15"}, {"name": "结果", "value": "20"}], "start": 1750991492298, "stop": 1750991492300, "uuid": "930b2586-33de-451d-b370-f7a656c388e7", "historyId": "467e476b619440f9346e9606f609a30c", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}