{"name": "Subtracting two numbers", "status": "skipped", "steps": [{"name": "Given I have a calculator", "status": "skipped", "start": 1750910440612, "stop": 1750910440612}, {"name": "Given I have entered 100 into the calculator", "status": "skipped", "start": 1750910440612, "stop": 1750910440612}, {"name": "And I have entered 30 into the calculator", "status": "skipped", "start": 1750910440612, "stop": 1750910440612}, {"name": "When I press subtract", "status": "skipped", "start": 1750910440612, "stop": 1750910440612}, {"name": "Then the result should be 70 on the screen", "status": "skipped", "start": 1750910440612, "stop": 1750910440612}], "start": 1750910440611, "stop": 1750910440612, "uuid": "1671d025-e005-43a3-b17e-c67db5ad885c", "historyId": "6e636c33aa713b30db7ead124d6e13cb", "fullName": "Calculator Operations: Subtracting two numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}