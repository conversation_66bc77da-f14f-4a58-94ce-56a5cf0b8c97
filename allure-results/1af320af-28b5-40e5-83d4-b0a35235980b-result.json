{"name": "Multiplying two numbers", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "5d699763-aec5-406e-9c21-2d4f7e1bcaa9-attachment.txt", "type": "text/plain"}], "start": 1750910677382, "stop": 1750910677383}, {"name": "Given I have entered 6 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "ce3ca642-76e5-45bc-8e02-0ac5c351b8a2-attachment.txt", "type": "text/plain"}], "start": 1750910677383, "stop": 1750910677384}, {"name": "And I have entered 7 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "5c1c95dd-d104-4cab-a10a-2c91eeab62bd-attachment.txt", "type": "text/plain"}], "start": 1750910677384, "stop": 1750910677385}, {"name": "When I press multiply", "status": "passed", "steps": [{"name": "Performing multiplication", "status": "passed", "attachments": [{"name": "Operation", "source": "10938af7-a72c-4f04-9219-363a1f7d1ae8-attachment.txt", "type": "text/plain"}], "start": 1750910677386, "stop": 1750910677387}], "start": 1750910677386, "stop": 1750910677387}, {"name": "Then the result should be 42 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 42", "status": "passed", "attachments": [{"name": "Result Verification", "source": "7805a760-380c-4376-871e-d164e1129a8c-attachment.txt", "type": "text/plain"}], "start": 1750910677388, "stop": 1750910677388}], "start": 1750910677387, "stop": 1750910677388}], "start": 1750910677379, "stop": 1750910677389, "uuid": "4a6ddac7-ff40-42e4-b48b-fc9014917dc8", "historyId": "c8e6447541b43bc24b5e60092c7fefbb", "fullName": "Calculator Operations: Multiplying two numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}