{"name": "两个数字相除", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750920588927, "stop": 1750920588927}, {"name": "Given 我已经输入数字 84 到计算器中", "status": "skipped", "start": 1750920588927, "stop": 1750920588927}, {"name": "And 我已经输入数字 12 到计算器中", "status": "skipped", "start": 1750920588927, "stop": 1750920588927}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750920588927, "stop": 1750920588927}, {"name": "Then 屏幕上应该显示结果 7", "status": "skipped", "start": 1750920588927, "stop": 1750920588927}], "start": 1750920588926, "stop": 1750920588927, "uuid": "ec3ca467-5bcf-4561-a96d-366a6fd892af", "historyId": "b173ac1221cc8eeeace98d836653d440", "fullName": "计算器功能: 两个数字相除", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}