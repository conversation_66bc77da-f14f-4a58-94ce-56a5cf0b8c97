{"name": "Dividing by zero should raise an error", "status": "skipped", "steps": [{"name": "Given I have a calculator", "status": "skipped", "start": 1750910440619, "stop": 1750910440620}, {"name": "Given I have entered 10 into the calculator", "status": "skipped", "start": 1750910440620, "stop": 1750910440620}, {"name": "And I have entered 0 into the calculator", "status": "skipped", "start": 1750910440620, "stop": 1750910440620}, {"name": "When I press divide", "status": "skipped", "start": 1750910440620, "stop": 1750910440620}, {"name": "Then I should see an error message \"Cannot divide by zero\"", "status": "skipped", "start": 1750910440620, "stop": 1750910440620}], "start": 1750910440618, "stop": 1750910440620, "uuid": "8ba42029-63de-43bb-9407-e2d7cc4d336c", "historyId": "cdaec262e72d3d3dd03fb3d987cc67cc", "fullName": "Calculator Operations: Dividing by zero should raise an error", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}