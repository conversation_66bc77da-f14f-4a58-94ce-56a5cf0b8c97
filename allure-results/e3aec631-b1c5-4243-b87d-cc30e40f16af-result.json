{"name": "Subtracting two numbers", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "a79f4350-68b5-4a07-af3f-627d6088454f-attachment.txt", "type": "text/plain"}], "start": 1750910677371, "stop": 1750910677371}, {"name": "Given I have entered 100 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "993b3586-957a-4449-aa5e-8136abf4e466-attachment.txt", "type": "text/plain"}], "start": 1750910677372, "stop": 1750910677372}, {"name": "And I have entered 30 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "5e91a94d-28eb-4bc2-a833-7f77f9d4d798-attachment.txt", "type": "text/plain"}], "start": 1750910677373, "stop": 1750910677373}, {"name": "When I press subtract", "status": "passed", "steps": [{"name": "Performing subtraction", "status": "passed", "attachments": [{"name": "Operation", "source": "0a3fddd2-fb52-4d97-a9fd-e84f5082613e-attachment.txt", "type": "text/plain"}], "start": 1750910677374, "stop": 1750910677375}], "start": 1750910677374, "stop": 1750910677375}, {"name": "Then the result should be 70 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 70", "status": "passed", "attachments": [{"name": "Result Verification", "source": "eb3b590c-7965-4c08-b858-2b617423274e-attachment.txt", "type": "text/plain"}], "start": 1750910677376, "stop": 1750910677377}], "start": 1750910677376, "stop": 1750910677377}], "start": 1750910677370, "stop": 1750910677378, "uuid": "0bd5f7d8-6f1b-49db-803d-ed1f77f6e555", "historyId": "6e636c33aa713b30db7ead124d6e13cb", "fullName": "Calculator Operations: Subtracting two numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}