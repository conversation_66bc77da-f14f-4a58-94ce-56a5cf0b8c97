{"name": "Dividing two numbers", "status": "skipped", "steps": [{"name": "Given I have a calculator", "status": "skipped", "start": 1750910440616, "stop": 1750910440616}, {"name": "Given I have entered 84 into the calculator", "status": "skipped", "start": 1750910440616, "stop": 1750910440616}, {"name": "And I have entered 12 into the calculator", "status": "skipped", "start": 1750910440616, "stop": 1750910440616}, {"name": "When I press divide", "status": "skipped", "start": 1750910440616, "stop": 1750910440616}, {"name": "Then the result should be 7 on the screen", "status": "skipped", "start": 1750910440616, "stop": 1750910440616}], "start": 1750910440615, "stop": 1750910440616, "uuid": "a3d98695-c643-4d3e-addb-ad8accd0f61c", "historyId": "36f36f57e718739bc89df3a31bafec65", "fullName": "Calculator Operations: Dividing two numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}