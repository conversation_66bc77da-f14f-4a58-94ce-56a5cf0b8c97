{"name": "两个正数相加", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937264189, "stop": 1750937264189}, {"name": "Given 我已经输入数字 50 到计算器中", "status": "skipped", "start": 1750937264189, "stop": 1750937264189}, {"name": "And 我已经输入数字 70 到计算器中", "status": "skipped", "start": 1750937264189, "stop": 1750937264189}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750937264189, "stop": 1750937264189}, {"name": "Then 屏幕上应该显示结果 120", "status": "skipped", "start": 1750937264189, "stop": 1750937264189}], "start": 1750937264188, "stop": 1750937264189, "uuid": "6ce1a01d-a6b9-4f29-96a0-bf5e417c302a", "historyId": "bb528dd71bbfcb703aa589df11393955", "fullName": "计算器功能: 两个正数相加", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}