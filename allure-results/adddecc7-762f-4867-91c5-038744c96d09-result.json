{"name": "测试报告书写", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937711846, "stop": 1750937711846}, {"name": "Given given: 测试111", "status": "skipped", "start": 1750937711846, "stop": 1750937711846}, {"name": "When when: 测试111", "status": "skipped", "start": 1750937711846, "stop": 1750937711846}, {"name": "Then then: 测试111", "status": "skipped", "start": 1750937711846, "stop": 1750937711846}], "start": 1750937711845, "stop": 1750937711846, "uuid": "a989fa6c-1fbb-4168-82d6-d975d77421c3", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}