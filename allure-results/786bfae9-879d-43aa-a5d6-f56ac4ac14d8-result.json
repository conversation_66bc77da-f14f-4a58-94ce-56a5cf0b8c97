{"name": "Adding two positive numbers", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "50ea2227-15b0-4b12-887f-2bdc07183b51-attachment.txt", "type": "text/plain"}], "start": 1750910677334, "stop": 1750910677336}, {"name": "Given I have entered 50 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "e83b5d86-715f-4d65-869f-5971e615fb19-attachment.txt", "type": "text/plain"}], "start": 1750910677337, "stop": 1750910677338}, {"name": "And I have entered 70 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "0d93eb85-d405-405f-ac0b-a5782e442672-attachment.txt", "type": "text/plain"}], "start": 1750910677338, "stop": 1750910677339}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "8bf94f8c-162c-4c52-b1a7-233b124e08d9-attachment.txt", "type": "text/plain"}], "start": 1750910677339, "stop": 1750910677339}], "start": 1750910677339, "stop": 1750910677339}, {"name": "Then the result should be 120 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 120", "status": "passed", "attachments": [{"name": "Result Verification", "source": "8fe0d58f-cfe0-4c00-83ff-b7af831f2a7a-attachment.txt", "type": "text/plain"}], "start": 1750910677340, "stop": 1750910677340}], "start": 1750910677340, "stop": 1750910677340}], "start": 1750910677332, "stop": 1750910677341, "uuid": "5a4993cc-8d20-4dc3-8b38-d3fbd223fb1a", "historyId": "355b4250f71164e51ec8529a901e5d7b", "fullName": "Calculator Operations: Adding two positive numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}