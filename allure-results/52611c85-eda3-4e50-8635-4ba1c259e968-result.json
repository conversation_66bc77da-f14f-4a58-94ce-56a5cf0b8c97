{"name": "除零应该抛出错误", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750920588929, "stop": 1750920588929}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750920588929, "stop": 1750920588929}, {"name": "And 我已经输入数字 0 到计算器中", "status": "skipped", "start": 1750920588929, "stop": 1750920588929}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750920588929, "stop": 1750920588929}, {"name": "Then 我应该看到错误信息 \"Cannot divide by zero\"", "status": "skipped", "start": 1750920588929, "stop": 1750920588929}], "start": 1750920588928, "stop": 1750920588929, "uuid": "dff4a616-a7dc-4900-91ea-225e32ebed2d", "historyId": "038434e4c66ac0b41d9341671e2a4539", "fullName": "计算器功能: 除零应该抛出错误", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}