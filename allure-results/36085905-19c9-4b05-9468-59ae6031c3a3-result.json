{"name": "不同数字的加法运算 -- @1.2 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "76ce7b9a-e9c8-4c67-85c7-1127a4d5aa2c-attachment.txt", "type": "text/plain"}], "start": 1750918797817, "stop": 1750918797818}, {"name": "Given 我已经输入数字 5 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "3d6226a0-c3fc-41ec-9730-430c27e405b1-attachment.txt", "type": "text/plain"}], "start": 1750918797818, "stop": 1750918797819}, {"name": "And 我已经输入数字 15 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "17d77df3-4bb8-4a7e-bd8d-e31acd887e93-attachment.txt", "type": "text/plain"}], "start": 1750918797819, "stop": 1750918797822}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "3aa275d0-0dfc-4450-844b-d629b199c09a-attachment.txt", "type": "text/plain"}], "start": 1750918797823, "stop": 1750918797824}], "start": 1750918797823, "stop": 1750918797824}, {"name": "Then 屏幕上应该显示结果 20", "status": "passed", "steps": [{"name": "Verifying result equals 20", "status": "passed", "attachments": [{"name": "Result Verification", "source": "2dcd0b50-b448-4806-9c2d-7452ecc1dd2f-attachment.txt", "type": "text/plain"}], "start": 1750918797825, "stop": 1750918797826}], "start": 1750918797824, "stop": 1750918797826}], "parameters": [{"name": "第一个数", "value": "5"}, {"name": "第二个数", "value": "15"}, {"name": "结果", "value": "20"}], "start": 1750918797815, "stop": 1750918797826, "uuid": "14f79f05-9bed-451a-97cb-2e559b45394e", "historyId": "467e476b619440f9346e9606f609a30c", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}