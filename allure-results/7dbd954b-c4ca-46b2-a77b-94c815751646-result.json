{"name": "不同数字的加法运算 -- @1.4 ", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750921248627, "stop": 1750921248627}, {"name": "Given 我已经输入数字 -10 到计算器中", "status": "skipped", "start": 1750921248627, "stop": 1750921248627}, {"name": "And 我已经输入数字 5 到计算器中", "status": "skipped", "start": 1750921248627, "stop": 1750921248627}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750921248628, "stop": 1750921248628}, {"name": "Then 屏幕上应该显示结果 -5", "status": "skipped", "start": 1750921248628, "stop": 1750921248628}], "parameters": [{"name": "第一个数", "value": "-10"}, {"name": "第二个数", "value": "5"}, {"name": "结果", "value": "-5"}], "start": 1750921248627, "stop": 1750921248628, "uuid": "61c7051a-272e-45c9-8e4a-c7435d0cde2c", "historyId": "19a7a37e9a802f6db0bfeca21d41fbf0", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}