{"name": "两个数字相减", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750923106698, "stop": 1750923106698}, {"name": "Given 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750923106698, "stop": 1750923106698}, {"name": "And 我已经输入数字 30 到计算器中", "status": "skipped", "start": 1750923106698, "stop": 1750923106698}, {"name": "When 我按下减法按钮", "status": "skipped", "start": 1750923106698, "stop": 1750923106698}, {"name": "Then 屏幕上应该显示结果 70", "status": "skipped", "start": 1750923106698, "stop": 1750923106698}], "start": 1750923106697, "stop": 1750923106698, "uuid": "750a3d65-24a5-4b04-b489-5b0ee7a98184", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "fullName": "计算器功能: 两个数字相减", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}