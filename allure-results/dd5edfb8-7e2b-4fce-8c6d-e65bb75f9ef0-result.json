{"name": "不同数字的加法运算 -- @1.2 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "0f64eadf-c36f-4a86-9e81-e6d303536483-attachment.txt", "type": "text/plain"}], "start": 1750937711800, "stop": 1750937711801}, {"name": "Given 我已经输入数字 5 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "3f4fee9c-301c-4567-9237-aafed8fb6a56-attachment.txt", "type": "text/plain"}], "start": 1750937711801, "stop": 1750937711802}, {"name": "And 我已经输入数字 15 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "244b6218-00d2-44b4-9880-5285efea6f83-attachment.txt", "type": "text/plain"}], "start": 1750937711803, "stop": 1750937711803}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "645f466e-0afa-4a37-942e-d52fc966d1dc-attachment.txt", "type": "text/plain"}], "start": 1750937711804, "stop": 1750937711805}], "start": 1750937711804, "stop": 1750937711805}, {"name": "Then 屏幕上应该显示结果 20", "status": "passed", "steps": [{"name": "Verifying result equals 20", "status": "passed", "attachments": [{"name": "Result Verification", "source": "9e0bb52d-1d34-4643-9916-5d0c1e6d5562-attachment.txt", "type": "text/plain"}], "start": 1750937711805, "stop": 1750937711806}], "start": 1750937711805, "stop": 1750937711806}], "parameters": [{"name": "第一个数", "value": "5"}, {"name": "第二个数", "value": "15"}, {"name": "结果", "value": "20"}], "start": 1750937711798, "stop": 1750937711806, "uuid": "8d0df251-9240-4ba3-bbab-dfb2d034d052", "historyId": "467e476b619440f9346e9606f609a30c", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}