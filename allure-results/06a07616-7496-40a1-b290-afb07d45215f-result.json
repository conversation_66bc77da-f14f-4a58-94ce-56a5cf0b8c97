{"name": "不同数字的加法运算 -- @1.1 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "6150b299-5c7d-4f65-a36d-fa2a5227faaf-attachment.txt", "type": "text/plain"}], "start": 1750918797807, "stop": 1750918797808}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "e1d6ea77-8b86-4e4b-b9b0-f73126f43650-attachment.txt", "type": "text/plain"}], "start": 1750918797808, "stop": 1750918797809}, {"name": "And 我已经输入数字 20 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "072e5456-89ea-4f4a-aaea-8f75fddd1e55-attachment.txt", "type": "text/plain"}], "start": 1750918797810, "stop": 1750918797810}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "187cfc6f-efc3-45e2-9ce7-6bb9d55cf166-attachment.txt", "type": "text/plain"}], "start": 1750918797811, "stop": 1750918797812}], "start": 1750918797811, "stop": 1750918797812}, {"name": "Then 屏幕上应该显示结果 30", "status": "passed", "steps": [{"name": "Verifying result equals 30", "status": "passed", "attachments": [{"name": "Result Verification", "source": "4b052a38-40f6-48b5-82df-22c5daacd4fc-attachment.txt", "type": "text/plain"}], "start": 1750918797812, "stop": 1750918797813}], "start": 1750918797812, "stop": 1750918797813}], "parameters": [{"name": "第一个数", "value": "10"}, {"name": "第二个数", "value": "20"}, {"name": "结果", "value": "30"}], "start": 1750918797806, "stop": 1750918797813, "uuid": "3de13708-0049-415f-a1ec-5bd6555e1277", "historyId": "d13f8d4ff0120894e90927ccbaa9a3df", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}