{"name": "不同数字的加法运算 -- @1.1 ", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937264191, "stop": 1750937264191}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750937264191, "stop": 1750937264191}, {"name": "And 我已经输入数字 20 到计算器中", "status": "skipped", "start": 1750937264191, "stop": 1750937264191}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750937264191, "stop": 1750937264191}, {"name": "Then 屏幕上应该显示结果 30", "status": "skipped", "start": 1750937264191, "stop": 1750937264191}], "parameters": [{"name": "第一个数", "value": "10"}, {"name": "第二个数", "value": "20"}, {"name": "结果", "value": "30"}], "start": 1750937264190, "stop": 1750937264191, "uuid": "f64c150e-9942-4dad-8890-423527d67789", "historyId": "d13f8d4ff0120894e90927ccbaa9a3df", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}