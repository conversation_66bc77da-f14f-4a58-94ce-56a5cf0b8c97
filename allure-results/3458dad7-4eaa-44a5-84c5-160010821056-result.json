{"name": "不同数字的加法运算 -- @1.4 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "23a832da-c09c-4fbc-9725-351e65dc5278-attachment.txt", "type": "text/plain"}], "start": 1750937711822, "stop": 1750937711823}, {"name": "Given 我已经输入数字 -10 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "0432ec9c-0285-49eb-a57d-2038ae76e2c7-attachment.txt", "type": "text/plain"}], "start": 1750937711824, "stop": 1750937711825}, {"name": "And 我已经输入数字 5 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "116f8983-8502-45be-82c6-ae9f916a0efb-attachment.txt", "type": "text/plain"}], "start": 1750937711826, "stop": 1750937711827}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "1659459c-cfad-417d-aaba-f1f476a13fb6-attachment.txt", "type": "text/plain"}], "start": 1750937711827, "stop": 1750937711828}], "start": 1750937711827, "stop": 1750937711828}, {"name": "Then 屏幕上应该显示结果 -5", "status": "passed", "steps": [{"name": "Verifying result equals -5", "status": "passed", "attachments": [{"name": "Result Verification", "source": "c9731378-932e-412f-a180-a84bb9e8b889-attachment.txt", "type": "text/plain"}], "start": 1750937711828, "stop": 1750937711829}], "start": 1750937711828, "stop": 1750937711829}], "parameters": [{"name": "第一个数", "value": "-10"}, {"name": "第二个数", "value": "5"}, {"name": "结果", "value": "-5"}], "start": 1750937711820, "stop": 1750937711829, "uuid": "85309100-1e11-48da-803b-031a1cd919a7", "historyId": "19a7a37e9a802f6db0bfeca21d41fbf0", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}