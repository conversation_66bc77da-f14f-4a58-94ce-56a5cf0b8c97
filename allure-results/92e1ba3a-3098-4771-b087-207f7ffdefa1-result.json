{"name": "两个正数相加", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "74970244-bf91-486d-b929-7277a0bba0be-attachment.txt", "type": "text/plain"}], "start": 1750918797797, "stop": 1750918797800}, {"name": "Given 我已经输入数字 50 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "9af19092-cad9-408e-addc-4df32ea395f5-attachment.txt", "type": "text/plain"}], "start": 1750918797801, "stop": 1750918797801}, {"name": "And 我已经输入数字 70 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "d0afac35-9cdc-46e8-8f3c-f56a1ddd2f75-attachment.txt", "type": "text/plain"}], "start": 1750918797802, "stop": 1750918797802}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "07cd4408-08e9-4a37-bc38-ed5392cbb06d-attachment.txt", "type": "text/plain"}], "start": 1750918797803, "stop": 1750918797803}], "start": 1750918797803, "stop": 1750918797803}, {"name": "Then 屏幕上应该显示结果 120", "status": "passed", "steps": [{"name": "Verifying result equals 120", "status": "passed", "attachments": [{"name": "Result Verification", "source": "cb5a6aa4-77c6-446e-9e21-9dd13f3e89f9-attachment.txt", "type": "text/plain"}], "start": 1750918797804, "stop": 1750918797804}], "start": 1750918797803, "stop": 1750918797804}], "start": 1750918797795, "stop": 1750918797805, "uuid": "caaaadea-fc8f-4475-8db3-c269c6a0bbe4", "historyId": "bb528dd71bbfcb703aa589df11393955", "fullName": "计算器功能: 两个正数相加", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}