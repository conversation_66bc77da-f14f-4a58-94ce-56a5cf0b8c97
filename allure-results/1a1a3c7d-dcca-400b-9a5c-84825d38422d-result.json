{"name": "测试报告书写", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "0a77f5d5-cdb9-493c-84db-f653865bf96b-attachment.txt", "type": "text/plain"}], "start": 1750921317480, "stop": 1750921317481}, {"name": "Given given: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "f22107fd-9405-4d72-a790-7cc63a8a87c6-attachment.txt", "type": "text/plain"}], "start": 1750921317482, "stop": 1750921317482}, {"name": "When when: 测试111", "status": "passed", "steps": [{"name": "步骤: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "35211689-685f-4d8f-97ef-a7949083f331-attachment.txt", "type": "text/plain"}], "start": 1750921317483, "stop": 1750921317484}], "start": 1750921317483, "stop": 1750921317484}, {"name": "Then then: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "6c472b6e-03f5-4841-aad0-638b4c4dbee3-attachment.txt", "type": "text/plain"}], "start": 1750921317485, "stop": 1750921317487}], "start": 1750921317479, "stop": 1750921317487, "uuid": "46b17cf6-6228-4dd2-bf04-84868cdf2a59", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}