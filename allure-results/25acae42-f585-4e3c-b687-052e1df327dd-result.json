{"name": "测试报告书写", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "38e6fe98-01e9-4772-b3cb-70b8044d5ffe-attachment.txt", "type": "text/plain"}], "start": 1750937352529, "stop": 1750937352530}, {"name": "Given given: 测试111", "status": "passed", "attachments": [{"name": "Log [INFO]", "source": "9c398e18-efb3-4f31-9b14-7820a9a641be-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "13fd2625-5491-4a65-82ce-8846207933bb-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "e3658d55-d203-4394-a140-2fe63bd79119-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "e0160299-e473-4e65-aaf3-1e6d561f693a-attachment.txt", "type": "text/plain"}], "start": 1750937352531, "stop": 1750937352535}, {"name": "When when: 测试111", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "attachments": [{"name": "Log [INFO]", "source": "c6a09e55-cd6e-46b8-8435-2eb9fdfab402-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "6a962790-967e-4da2-84f2-3e7f8492530f-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "94a6b441-1cac-4fab-a4c8-97a8047f4fe1-attachment.txt", "type": "text/plain"}], "start": 1750937352535, "stop": 1750937352541}, {"name": "Then then: 测试111", "status": "skipped", "start": 1750937352542, "stop": 1750937352542}], "start": 1750937352527, "stop": 1750937352542, "uuid": "d3dff8ee-27b3-4315-a58c-81f9583c6f81", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}