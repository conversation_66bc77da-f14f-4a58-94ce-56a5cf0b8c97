{"name": "Adding different numbers -- @1.3 ", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "8b7bf54c-2a5a-4fea-99d3-e755120e9f1f-attachment.txt", "type": "text/plain"}], "start": 1750910440593, "stop": 1750910440594}, {"name": "Given I have entered 0 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "3bce378c-67df-41b6-8d13-5ae290d0d799-attachment.txt", "type": "text/plain"}], "start": 1750910440594, "stop": 1750910440595}, {"name": "And I have entered 100 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "6291446b-988b-4f7d-9d64-6bf354f95717-attachment.txt", "type": "text/plain"}], "start": 1750910440595, "stop": 1750910440596}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "4ca7cf93-451a-473c-851c-f23999f22660-attachment.txt", "type": "text/plain"}], "start": 1750910440597, "stop": 1750910440597}], "start": 1750910440597, "stop": 1750910440597}, {"name": "Then the result should be 100 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 100", "status": "passed", "attachments": [{"name": "Result Verification", "source": "21040338-ea1e-40c8-8056-9d46f5adb17e-attachment.txt", "type": "text/plain"}], "start": 1750910440598, "stop": 1750910440598}], "start": 1750910440598, "stop": 1750910440598}], "parameters": [{"name": "first", "value": "0"}, {"name": "second", "value": "100"}, {"name": "result", "value": "100"}], "start": 1750910440591, "stop": 1750910440599, "uuid": "69e582e4-3125-488b-b641-1402594470d0", "historyId": "0ec47a2af2b5dc4d5425cfbe164d1849", "fullName": "Calculator Operations: Adding different numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}