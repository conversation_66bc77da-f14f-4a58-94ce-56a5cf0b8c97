{"name": "测试报告书写", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "da01a5a9-a62c-45f9-99a2-38e07ed520b6-attachment.txt", "type": "text/plain"}], "start": 1750920808265, "stop": 1750920808266}, {"name": "Given given: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "c1221cd7-1036-4a84-9704-31da743f9a69-attachment.txt", "type": "text/plain"}], "start": 1750920808266, "stop": 1750920808267}, {"name": "When when: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "e1e7bc4d-bce6-4db9-863b-800af02b594a-attachment.txt", "type": "text/plain"}], "start": 1750920808267, "stop": 1750920808268}, {"name": "Then then: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "88c93350-8269-41d7-ad81-dd31f8a44965-attachment.txt", "type": "text/plain"}], "start": 1750920808268, "stop": 1750920808269}], "start": 1750920808263, "stop": 1750920808269, "uuid": "08dac754-5a4d-440c-a8cb-b324e13c30bb", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}