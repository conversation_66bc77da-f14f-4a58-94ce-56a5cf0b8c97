{"name": "不同数字的加法运算 -- @1.4 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "eefb0444-1ce7-445f-a84e-4902eb389790-attachment.txt", "type": "text/plain"}], "start": 1750918797841, "stop": 1750918797842}, {"name": "Given 我已经输入数字 -10 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "4ff2b91c-aa79-460d-b4b9-b7d45d8ac910-attachment.txt", "type": "text/plain"}], "start": 1750918797842, "stop": 1750918797843}, {"name": "And 我已经输入数字 5 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "b5635b5d-16e1-4462-9093-66683913d58f-attachment.txt", "type": "text/plain"}], "start": 1750918797844, "stop": 1750918797845}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "765eb914-b7b9-47cc-9f3f-4cce57ba52ee-attachment.txt", "type": "text/plain"}], "start": 1750918797845, "stop": 1750918797846}], "start": 1750918797845, "stop": 1750918797846}, {"name": "Then 屏幕上应该显示结果 -5", "status": "passed", "steps": [{"name": "Verifying result equals -5", "status": "passed", "attachments": [{"name": "Result Verification", "source": "35439b4b-e278-406c-ac1a-c4fe0dd18da9-attachment.txt", "type": "text/plain"}], "start": 1750918797847, "stop": 1750918797849}], "start": 1750918797847, "stop": 1750918797849}], "parameters": [{"name": "第一个数", "value": "-10"}, {"name": "第二个数", "value": "5"}, {"name": "结果", "value": "-5"}], "start": 1750918797839, "stop": 1750918797849, "uuid": "de5c3a75-e626-46b1-99cc-f2b179f8ac5b", "historyId": "19a7a37e9a802f6db0bfeca21d41fbf0", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}