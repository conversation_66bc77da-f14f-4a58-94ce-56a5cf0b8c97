{"name": "除零应该抛出错误", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "1c98fce4-6a06-4f9a-abf3-2acc73ac2974-attachment.txt", "type": "text/plain"}], "start": 1750918797895, "stop": 1750918797896}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "423e0827-62ce-4a78-b582-47e02383bb99-attachment.txt", "type": "text/plain"}], "start": 1750918797897, "stop": 1750918797898}, {"name": "And 我已经输入数字 0 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "064a9596-4ea9-438c-87b2-b074f0e8ce10-attachment.txt", "type": "text/plain"}], "start": 1750918797898, "stop": 1750918797899}, {"name": "When 我按下除法按钮", "status": "passed", "steps": [{"name": "Performing division", "status": "passed", "attachments": [{"name": "Error", "source": "c474bc5e-cef1-42ce-951c-ef5a77ef1718-attachment.txt", "type": "text/plain"}], "start": 1750918797900, "stop": 1750918797901}], "start": 1750918797899, "stop": 1750918797901}, {"name": "Then 我应该看到错误信息 \"Cannot divide by zero\"", "status": "passed", "steps": [{"name": "Verifying error message: Cannot divide by zero", "status": "passed", "attachments": [{"name": "Error Verification", "source": "fdb16999-b3e0-46c1-9686-077aa061f2f1-attachment.txt", "type": "text/plain"}], "start": 1750918797903, "stop": 1750918797906}], "start": 1750918797902, "stop": 1750918797906}], "start": 1750918797892, "stop": 1750918797907, "uuid": "12b20b78-5670-44c7-9b9d-5f4ec4892a94", "historyId": "038434e4c66ac0b41d9341671e2a4539", "fullName": "计算器功能: 除零应该抛出错误", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}