{"name": "两个数字相乘", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750921422260, "stop": 1750921422260}, {"name": "Given 我已经输入数字 6 到计算器中", "status": "skipped", "start": 1750921422260, "stop": 1750921422260}, {"name": "And 我已经输入数字 7 到计算器中", "status": "skipped", "start": 1750921422260, "stop": 1750921422260}, {"name": "When 我按下乘法按钮", "status": "skipped", "start": 1750921422260, "stop": 1750921422260}, {"name": "Then 屏幕上应该显示结果 42", "status": "skipped", "start": 1750921422260, "stop": 1750921422260}], "start": 1750921422259, "stop": 1750921422260, "uuid": "98bb12a8-5d95-4534-ae95-6681aed8a187", "historyId": "f3662b2d33db3b7b0965a67f9fdce563", "fullName": "计算器功能: 两个数字相乘", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}