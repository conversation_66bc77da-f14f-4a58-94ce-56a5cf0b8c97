{"name": "测试报告书写", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "f3ef9536-0fa0-478a-bc15-e0058c24b6ff-attachment.txt", "type": "text/plain"}], "start": 1750937264217, "stop": 1750937264218}, {"name": "Given given: 测试111", "status": "passed", "attachments": [{"name": "Log [INFO]", "source": "b5cbca8e-c46e-495c-9f50-0cad4a4af30d-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "ed8927e3-614a-498b-800c-1fb197576684-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "99df88a1-9920-4713-83c7-4620b9acaa96-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "29818419-0224-4f9a-9b12-04e1d5c235b4-attachment.txt", "type": "text/plain"}], "start": 1750937264219, "stop": 1750937264223}, {"name": "When when: 测试111", "status": "passed", "attachments": [{"name": "Log [INFO]", "source": "fda8df64-23aa-44ab-b96c-2ecae9ad367b-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "366b0155-1e65-4990-9621-217c86c0c0d9-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "08e1e5f4-b7e9-4563-81bd-6a095737657d-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "c309e68a-e6a8-4904-aea7-b50b938a6268-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "bcabfd02-f334-4f27-8d90-cff480abe614-attachment.txt", "type": "text/plain"}], "start": 1750937264224, "stop": 1750937264229}, {"name": "Then then: 测试111", "status": "passed", "attachments": [{"name": "Log [INFO]", "source": "d5c5ad44-bc7c-4985-9f56-a0b81c66efd1-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "8f1bca00-5b4f-4e63-9f15-1c2f7a7425f4-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "a7d16dec-3898-481a-b7d0-ab3313d4f57a-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "343cee63-3067-4ae3-af38-ea11a5c7ea14-attachment.txt", "type": "text/plain"}, {"name": "Log [INFO]", "source": "f1618b7c-c8f3-4419-9807-c232b4684055-attachment.txt", "type": "text/plain"}], "start": 1750937264229, "stop": 1750937264235}], "start": 1750937264215, "stop": 1750937264235, "uuid": "f33eaacf-5fe1-41ea-b16b-c9664059314d", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}