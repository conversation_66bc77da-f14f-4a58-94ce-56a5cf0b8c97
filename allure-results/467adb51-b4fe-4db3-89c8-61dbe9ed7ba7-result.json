{"name": "除零应该抛出错误", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937264208, "stop": 1750937264208}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750937264208, "stop": 1750937264208}, {"name": "And 我已经输入数字 0 到计算器中", "status": "skipped", "start": 1750937264208, "stop": 1750937264208}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750937264209, "stop": 1750937264209}, {"name": "Then 我应该看到错误信息 \"Cannot divide by zero\"", "status": "skipped", "start": 1750937264209, "stop": 1750937264209}], "start": 1750937264207, "stop": 1750937264209, "uuid": "a919b70c-5a59-4f6e-a902-a1a52c8058f5", "historyId": "038434e4c66ac0b41d9341671e2a4539", "fullName": "计算器功能: 除零应该抛出错误", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}