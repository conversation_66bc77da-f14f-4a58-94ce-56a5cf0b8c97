{"name": "不同数字的加法运算 -- @1.3 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "da65b807-8cc6-4830-9f92-2b38644d9e81-attachment.txt", "type": "text/plain"}], "start": 1750918797833, "stop": 1750918797834}, {"name": "Given 我已经输入数字 0 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "73e701dd-3721-4ef0-9be4-bcc683e877aa-attachment.txt", "type": "text/plain"}], "start": 1750918797834, "stop": 1750918797835}, {"name": "And 我已经输入数字 100 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "07cf1e4c-2634-4c26-ab0a-09fb9226eb2e-attachment.txt", "type": "text/plain"}], "start": 1750918797836, "stop": 1750918797836}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "18374762-d3ee-4f4a-a225-43c7e1386374-attachment.txt", "type": "text/plain"}], "start": 1750918797837, "stop": 1750918797837}], "start": 1750918797837, "stop": 1750918797837}, {"name": "Then 屏幕上应该显示结果 100", "status": "passed", "steps": [{"name": "Verifying result equals 100", "status": "passed", "attachments": [{"name": "Result Verification", "source": "b0947cca-de51-40cb-8db8-ab6acd31421d-attachment.txt", "type": "text/plain"}], "start": 1750918797838, "stop": 1750918797838}], "start": 1750918797838, "stop": 1750918797838}], "parameters": [{"name": "第一个数", "value": "0"}, {"name": "第二个数", "value": "100"}, {"name": "结果", "value": "100"}], "start": 1750918797830, "stop": 1750918797838, "uuid": "7831beb4-3c8b-4bd9-ade1-2eb52fe3daac", "historyId": "17d7009b30795a79145207c83df82232", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}