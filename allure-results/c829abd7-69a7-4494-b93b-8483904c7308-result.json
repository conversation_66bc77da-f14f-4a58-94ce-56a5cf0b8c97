{"name": "测试报告书写", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "a1bfaa28-a74a-473a-a7fa-63873259133f-attachment.txt", "type": "text/plain"}], "start": 1750991358525, "stop": 1750991358526}, {"name": "Given given: 测试111", "status": "passed", "start": 1750991358527, "stop": 1750991358528}, {"name": "When when: 测试111", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "start": 1750991358529, "stop": 1750991358532}, {"name": "Then then: 测试111", "status": "skipped", "start": 1750991358533, "stop": 1750991358533}], "attachments": [{"name": "Scenario Logs - 测试报告书写", "source": "d6da983c-5ed4-42c3-9a52-4632854c6018-attachment.txt", "type": "text/plain"}], "start": 1750991358523, "stop": 1750991358533, "uuid": "f71738a8-8dec-4b14-be3e-ce587c1251ce", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}