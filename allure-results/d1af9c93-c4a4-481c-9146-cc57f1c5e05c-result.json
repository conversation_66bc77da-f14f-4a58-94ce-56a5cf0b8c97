{"name": "两个数字相乘", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937711834, "stop": 1750937711834}, {"name": "Given 我已经输入数字 6 到计算器中", "status": "skipped", "start": 1750937711834, "stop": 1750937711834}, {"name": "And 我已经输入数字 7 到计算器中", "status": "skipped", "start": 1750937711834, "stop": 1750937711834}, {"name": "When 我按下乘法按钮", "status": "skipped", "start": 1750937711834, "stop": 1750937711834}, {"name": "Then 屏幕上应该显示结果 42", "status": "skipped", "start": 1750937711834, "stop": 1750937711834}], "start": 1750937711833, "stop": 1750937711834, "uuid": "2e8b0c0a-f3e1-45e4-8401-a6dbc14d507a", "historyId": "f3662b2d33db3b7b0965a67f9fdce563", "fullName": "计算器功能: 两个数字相乘", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}