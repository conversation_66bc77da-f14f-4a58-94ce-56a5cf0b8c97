{"name": "测试报告书写", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "a760b6fd-cee2-4440-89c4-788b32a6ef4c-attachment.txt", "type": "text/plain"}], "start": 1750991492325, "stop": 1750991492327}, {"name": "Given given: 测试111", "status": "passed", "start": 1750991492327, "stop": 1750991492329}, {"name": "When when: 测试111", "status": "passed", "start": 1750991492329, "stop": 1750991492331}, {"name": "Then then: 测试111", "status": "passed", "start": 1750991492331, "stop": 1750991492333}, {"name": "When when: 测试111", "status": "passed", "start": 1750991492333, "stop": 1750991492334}, {"name": "When when: 测试111", "status": "passed", "start": 1750991492335, "stop": 1750991492337}, {"name": "When when: 测试111", "status": "passed", "start": 1750991492338, "stop": 1750991492340}, {"name": "When when: 测试111", "status": "passed", "start": 1750991492340, "stop": 1750991492345}], "attachments": [{"name": "Scenario Logs - 测试报告书写", "source": "2827b01c-793e-4b38-975b-d75676241e4f-attachment.txt", "type": "text/plain"}], "start": 1750991492323, "stop": 1750991492347, "uuid": "4e9575e1-9b1d-48e0-b42d-7c1b54356e52", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}