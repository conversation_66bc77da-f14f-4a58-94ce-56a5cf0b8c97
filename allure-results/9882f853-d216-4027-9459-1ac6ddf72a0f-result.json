{"name": "测试报告书写", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "34f065b2-00b5-4df4-a008-5565ce8e57c2-attachment.txt", "type": "text/plain"}], "start": 1750920588938, "stop": 1750920588939}, {"name": "Given given: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "68ccd5c9-6e39-4135-9d71-7cb21391d386-attachment.txt", "type": "text/plain"}], "start": 1750920588939, "stop": 1750920588939}, {"name": "When when: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "d370e9a4-bbe4-4b0a-93ea-58346ce61a0f-attachment.txt", "type": "text/plain"}], "start": 1750920588940, "stop": 1750920588940}, {"name": "Then then: 测试111", "status": "passed", "attachments": [{"name": "Input", "source": "1950f780-1891-4cef-a030-afa0f0c9fddc-attachment.txt", "type": "text/plain"}], "start": 1750920588941, "stop": 1750920588941}], "start": 1750920588937, "stop": 1750920588941, "uuid": "0e287d8e-18ac-4a2a-87b5-c1c3c6d49393", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}