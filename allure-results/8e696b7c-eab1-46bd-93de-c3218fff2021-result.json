{"name": "计算器维护计算历史记录", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "And 我已经输入数字 5 到计算器中", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "Then 屏幕上应该显示结果 15", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "And 计算历史记录应该包含 \"10 + 5 = 15\"", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "When 我已经输入数字 3 到计算器中", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "And 我按下乘法按钮", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "Then 屏幕上应该显示结果 45", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}, {"name": "And 计算历史记录应该包含 \"15 * 3 = 45\"", "status": "skipped", "start": 1750937711844, "stop": 1750937711844}], "start": 1750937711843, "stop": 1750937711844, "uuid": "987da923-5a08-4604-9830-281d521fdd33", "historyId": "2212440e47c6241ce16f9a6ef923a25b", "fullName": "计算器功能: 计算器维护计算历史记录", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}