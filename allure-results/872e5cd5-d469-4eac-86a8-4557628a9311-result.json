{"name": "不同数字的加法运算 -- @1.1 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "e7c7a673-3669-405d-a280-b629646ea812-attachment.txt", "type": "text/plain"}], "start": 1750937711792, "stop": 1750937711793}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "45f6ebf6-f442-4504-b447-bcb2dbef8812-attachment.txt", "type": "text/plain"}], "start": 1750937711793, "stop": 1750937711794}, {"name": "And 我已经输入数字 20 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "6e569f0e-7ad5-47d7-af3b-3b84c249d685-attachment.txt", "type": "text/plain"}], "start": 1750937711794, "stop": 1750937711795}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "f8b08bee-542b-4966-88fc-95d3590c11c5-attachment.txt", "type": "text/plain"}], "start": 1750937711795, "stop": 1750937711796}], "start": 1750937711795, "stop": 1750937711796}, {"name": "Then 屏幕上应该显示结果 30", "status": "passed", "steps": [{"name": "Verifying result equals 30", "status": "passed", "attachments": [{"name": "Result Verification", "source": "dff867d7-a2a3-460d-8041-b8e327a8b71e-attachment.txt", "type": "text/plain"}], "start": 1750937711796, "stop": 1750937711797}], "start": 1750937711796, "stop": 1750937711797}], "parameters": [{"name": "第一个数", "value": "10"}, {"name": "第二个数", "value": "20"}, {"name": "结果", "value": "30"}], "start": 1750937711789, "stop": 1750937711797, "uuid": "0d7f2851-b98e-4187-b010-7c3c2d55627f", "historyId": "d13f8d4ff0120894e90927ccbaa9a3df", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}