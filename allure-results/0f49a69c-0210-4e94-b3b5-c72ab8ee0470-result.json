{"name": "两个数字相乘", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937264205, "stop": 1750937264205}, {"name": "Given 我已经输入数字 6 到计算器中", "status": "skipped", "start": 1750937264205, "stop": 1750937264205}, {"name": "And 我已经输入数字 7 到计算器中", "status": "skipped", "start": 1750937264205, "stop": 1750937264205}, {"name": "When 我按下乘法按钮", "status": "skipped", "start": 1750937264205, "stop": 1750937264205}, {"name": "Then 屏幕上应该显示结果 42", "status": "skipped", "start": 1750937264205, "stop": 1750937264205}], "start": 1750937264204, "stop": 1750937264205, "uuid": "d1e5147d-253c-4f48-baa7-9b7e8df5b49c", "historyId": "f3662b2d33db3b7b0965a67f9fdce563", "fullName": "计算器功能: 两个数字相乘", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}