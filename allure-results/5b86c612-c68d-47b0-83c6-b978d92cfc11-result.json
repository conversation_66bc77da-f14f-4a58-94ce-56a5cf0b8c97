{"name": "Adding different numbers -- @1.4 ", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "b18261c0-958e-493d-b0d1-0e05577bdca6-attachment.txt", "type": "text/plain"}], "start": 1750910677362, "stop": 1750910677363}, {"name": "Given I have entered -10 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "3d1a792d-de56-4cbc-a884-e420ae003e70-attachment.txt", "type": "text/plain"}], "start": 1750910677363, "stop": 1750910677364}, {"name": "And I have entered 5 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "e6990a89-6420-4a22-bdda-afd0f9a82fcf-attachment.txt", "type": "text/plain"}], "start": 1750910677364, "stop": 1750910677365}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "e9b79af8-ab7f-46d5-869e-fee8591ead15-attachment.txt", "type": "text/plain"}], "start": 1750910677366, "stop": 1750910677366}], "start": 1750910677366, "stop": 1750910677366}, {"name": "Then the result should be -5 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals -5", "status": "passed", "attachments": [{"name": "Result Verification", "source": "b4320087-0550-46dc-9c58-155221aac0e2-attachment.txt", "type": "text/plain"}], "start": 1750910677367, "stop": 1750910677368}], "start": 1750910677367, "stop": 1750910677368}], "parameters": [{"name": "first", "value": "-10"}, {"name": "second", "value": "5"}, {"name": "result", "value": "-5"}], "start": 1750910677361, "stop": 1750910677368, "uuid": "4a80f711-d257-49bd-84ba-494a62155597", "historyId": "7a0a865e0e4954d9fc2804c1a09bdd33", "fullName": "Calculator Operations: Adding different numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}