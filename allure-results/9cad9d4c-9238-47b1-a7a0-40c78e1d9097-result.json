{"name": "两个数字相减", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937711832, "stop": 1750937711832}, {"name": "Given 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750937711832, "stop": 1750937711832}, {"name": "And 我已经输入数字 30 到计算器中", "status": "skipped", "start": 1750937711832, "stop": 1750937711832}, {"name": "When 我按下减法按钮", "status": "skipped", "start": 1750937711832, "stop": 1750937711832}, {"name": "Then 屏幕上应该显示结果 70", "status": "skipped", "start": 1750937711832, "stop": 1750937711832}], "start": 1750937711831, "stop": 1750937711832, "uuid": "a15a4585-fb64-48d0-9ffa-0c0c78577a25", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "fullName": "计算器功能: 两个数字相减", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}