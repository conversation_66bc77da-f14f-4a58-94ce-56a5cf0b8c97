{"name": "两个数字相除", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "67cc0e55-e367-4c0a-94ce-05380b47d284-attachment.txt", "type": "text/plain"}], "start": 1750918797883, "stop": 1750918797884}, {"name": "Given 我已经输入数字 84 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "cb22818e-35d0-40f1-b577-85f5628e4269-attachment.txt", "type": "text/plain"}], "start": 1750918797884, "stop": 1750918797885}, {"name": "And 我已经输入数字 12 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "caf3701a-8b05-431e-a4bf-188ef790834d-attachment.txt", "type": "text/plain"}], "start": 1750918797886, "stop": 1750918797886}, {"name": "When 我按下除法按钮", "status": "passed", "steps": [{"name": "Performing division", "status": "passed", "attachments": [{"name": "Operation", "source": "6b3b3eac-bc0a-4e96-a974-de17b8449da1-attachment.txt", "type": "text/plain"}], "start": 1750918797887, "stop": 1750918797888}], "start": 1750918797887, "stop": 1750918797888}, {"name": "Then 屏幕上应该显示结果 7", "status": "passed", "steps": [{"name": "Verifying result equals 7", "status": "passed", "attachments": [{"name": "Result Verification", "source": "32fb98ca-c349-45af-9622-bc29f61c4e34-attachment.txt", "type": "text/plain"}], "start": 1750918797888, "stop": 1750918797890}], "start": 1750918797888, "stop": 1750918797890}], "start": 1750918797878, "stop": 1750918797891, "uuid": "8c1257f5-64b7-46f9-bc1c-e86366b336c3", "historyId": "b173ac1221cc8eeeace98d836653d440", "fullName": "计算器功能: 两个数字相除", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}