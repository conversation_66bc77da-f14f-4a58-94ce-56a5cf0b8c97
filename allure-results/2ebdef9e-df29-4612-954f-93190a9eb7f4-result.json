{"name": "两个数字相乘", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750921248633, "stop": 1750921248633}, {"name": "Given 我已经输入数字 6 到计算器中", "status": "skipped", "start": 1750921248633, "stop": 1750921248633}, {"name": "And 我已经输入数字 7 到计算器中", "status": "skipped", "start": 1750921248633, "stop": 1750921248633}, {"name": "When 我按下乘法按钮", "status": "skipped", "start": 1750921248633, "stop": 1750921248633}, {"name": "Then 屏幕上应该显示结果 42", "status": "skipped", "start": 1750921248633, "stop": 1750921248633}], "start": 1750921248632, "stop": 1750921248633, "uuid": "eb85fd3a-26a9-411e-8c5e-d2ad00b3a5f6", "historyId": "f3662b2d33db3b7b0965a67f9fdce563", "fullName": "计算器功能: 两个数字相乘", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}