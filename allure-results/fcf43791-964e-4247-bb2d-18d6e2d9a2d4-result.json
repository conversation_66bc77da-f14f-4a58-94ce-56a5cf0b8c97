{"name": "Adding two positive numbers", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "2cb0b5b0-c8ae-4b86-8ec8-cd9c78efb021-attachment.txt", "type": "text/plain"}], "start": 1750910440559, "stop": 1750910440561}, {"name": "Given I have entered 50 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "7cb11921-17f4-4506-af7c-c28a94d686d0-attachment.txt", "type": "text/plain"}], "start": 1750910440563, "stop": 1750910440564}, {"name": "And I have entered 70 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "8fed48d3-00e4-4189-8ea7-46cba15bf126-attachment.txt", "type": "text/plain"}], "start": 1750910440565, "stop": 1750910440565}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "df085b6c-8870-49cd-916b-5904c0e660e0-attachment.txt", "type": "text/plain"}], "start": 1750910440566, "stop": 1750910440567}], "start": 1750910440566, "stop": 1750910440567}, {"name": "Then the result should be 120 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 120", "status": "passed", "attachments": [{"name": "Result Verification", "source": "550395f6-922c-42ff-b0d8-bf7981e27e3c-attachment.txt", "type": "text/plain"}], "start": 1750910440568, "stop": 1750910440568}], "start": 1750910440567, "stop": 1750910440568}], "start": 1750910440557, "stop": 1750910440569, "uuid": "7cc1d702-42c0-4725-9e30-cefd50b09f40", "historyId": "355b4250f71164e51ec8529a901e5d7b", "fullName": "Calculator Operations: Adding two positive numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}