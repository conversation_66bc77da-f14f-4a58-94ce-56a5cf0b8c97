{"name": "不同数字的加法运算 -- @1.3 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "509eaef2-ea08-462a-ad44-f2b47b627753-attachment.txt", "type": "text/plain"}], "start": 1750937711810, "stop": 1750937711811}, {"name": "Given 我已经输入数字 0 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "8e23b8f3-7742-48e5-bd7b-39d9bd77bf28-attachment.txt", "type": "text/plain"}], "start": 1750937711812, "stop": 1750937711813}, {"name": "And 我已经输入数字 100 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "dde746fa-07f1-463a-88bf-9134a5084605-attachment.txt", "type": "text/plain"}], "start": 1750937711813, "stop": 1750937711814}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "7ba0f5c3-5ea4-46a0-9117-2afc2ae84221-attachment.txt", "type": "text/plain"}], "start": 1750937711815, "stop": 1750937711816}], "start": 1750937711815, "stop": 1750937711816}, {"name": "Then 屏幕上应该显示结果 100", "status": "passed", "steps": [{"name": "Verifying result equals 100", "status": "passed", "attachments": [{"name": "Result Verification", "source": "38020988-cbfb-4e83-b563-647d516d2e44-attachment.txt", "type": "text/plain"}], "start": 1750937711818, "stop": 1750937711818}], "start": 1750937711817, "stop": 1750937711818}], "parameters": [{"name": "第一个数", "value": "0"}, {"name": "第二个数", "value": "100"}, {"name": "结果", "value": "100"}], "start": 1750937711808, "stop": 1750937711819, "uuid": "ac634f18-9749-4bdf-8481-416a299068c0", "historyId": "17d7009b30795a79145207c83df82232", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}