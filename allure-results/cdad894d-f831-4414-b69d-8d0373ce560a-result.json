{"name": "两个数字相减", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750991358512, "stop": 1750991358512}, {"name": "Given 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750991358512, "stop": 1750991358512}, {"name": "And 我已经输入数字 30 到计算器中", "status": "skipped", "start": 1750991358512, "stop": 1750991358512}, {"name": "When 我按下减法按钮", "status": "skipped", "start": 1750991358512, "stop": 1750991358512}, {"name": "Then 屏幕上应该显示结果 70", "status": "skipped", "start": 1750991358512, "stop": 1750991358512}], "start": 1750991358511, "stop": 1750991358512, "uuid": "96a693a4-232e-4d89-bc60-32e9696e293f", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "fullName": "计算器功能: 两个数字相减", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}