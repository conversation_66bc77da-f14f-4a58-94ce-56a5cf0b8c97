{"name": "Dividing two numbers", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "e1834b55-b388-4b23-8cf9-7fd6857ebf28-attachment.txt", "type": "text/plain"}], "start": 1750910677392, "stop": 1750910677392}, {"name": "Given I have entered 84 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "fedcd3fd-4e1c-4964-b176-1deddba6b8d1-attachment.txt", "type": "text/plain"}], "start": 1750910677393, "stop": 1750910677393}, {"name": "And I have entered 12 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "a17eae58-a451-4a21-853e-5c780aa35985-attachment.txt", "type": "text/plain"}], "start": 1750910677394, "stop": 1750910677395}, {"name": "When I press divide", "status": "passed", "steps": [{"name": "Performing division", "status": "passed", "attachments": [{"name": "Operation", "source": "76cb2edb-e4c3-4dc1-951c-1b83343e7548-attachment.txt", "type": "text/plain"}], "start": 1750910677396, "stop": 1750910677396}], "start": 1750910677395, "stop": 1750910677396}, {"name": "Then the result should be 7 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 7", "status": "passed", "attachments": [{"name": "Result Verification", "source": "a20236e2-69c5-473f-994d-2e14ea612ab5-attachment.txt", "type": "text/plain"}], "start": 1750910677396, "stop": 1750910677397}], "start": 1750910677396, "stop": 1750910677397}], "start": 1750910677391, "stop": 1750910677397, "uuid": "37c40c81-e7ed-47f0-9b70-5153ccd3a936", "historyId": "36f36f57e718739bc89df3a31bafec65", "fullName": "Calculator Operations: Dividing two numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}