{"name": "两个数字相减", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937264202, "stop": 1750937264202}, {"name": "Given 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750937264202, "stop": 1750937264202}, {"name": "And 我已经输入数字 30 到计算器中", "status": "skipped", "start": 1750937264203, "stop": 1750937264203}, {"name": "When 我按下减法按钮", "status": "skipped", "start": 1750937264203, "stop": 1750937264203}, {"name": "Then 屏幕上应该显示结果 70", "status": "skipped", "start": 1750937264203, "stop": 1750937264203}], "start": 1750937264201, "stop": 1750937264203, "uuid": "562ad74a-fb85-4376-8173-de050540e52c", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "fullName": "计算器功能: 两个数字相减", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}