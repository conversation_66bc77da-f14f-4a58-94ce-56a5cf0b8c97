{"name": "Adding different numbers -- @1.2 ", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "a81baea3-9d68-474e-9d17-4bbbe26f7987-attachment.txt", "type": "text/plain"}], "start": 1750910677349, "stop": 1750910677350}, {"name": "Given I have entered 5 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "a852e1f9-6c0a-4876-9c55-a8cf84ca6753-attachment.txt", "type": "text/plain"}], "start": 1750910677350, "stop": 1750910677351}, {"name": "And I have entered 15 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "dde55917-c891-4959-b81c-89a70789a760-attachment.txt", "type": "text/plain"}], "start": 1750910677352, "stop": 1750910677353}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "ece31b68-5b47-4137-be1b-5fcb62bc8106-attachment.txt", "type": "text/plain"}], "start": 1750910677353, "stop": 1750910677353}], "start": 1750910677353, "stop": 1750910677353}, {"name": "Then the result should be 20 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 20", "status": "passed", "attachments": [{"name": "Result Verification", "source": "9251d732-7cfd-4e08-b821-03cce53d160a-attachment.txt", "type": "text/plain"}], "start": 1750910677354, "stop": 1750910677354}], "start": 1750910677354, "stop": 1750910677354}], "parameters": [{"name": "first", "value": "5"}, {"name": "second", "value": "15"}, {"name": "result", "value": "20"}], "start": 1750910677348, "stop": 1750910677354, "uuid": "7a3b9492-973e-47bc-a7c0-cf774e4b9313", "historyId": "e9366fe59f8c428b5a48f708dab6a760", "fullName": "Calculator Operations: Adding different numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}