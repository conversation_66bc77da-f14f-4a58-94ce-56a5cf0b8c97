{"name": "两个数字相除", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750920808254, "stop": 1750920808254}, {"name": "Given 我已经输入数字 84 到计算器中", "status": "skipped", "start": 1750920808254, "stop": 1750920808254}, {"name": "And 我已经输入数字 12 到计算器中", "status": "skipped", "start": 1750920808254, "stop": 1750920808254}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750920808254, "stop": 1750920808254}, {"name": "Then 屏幕上应该显示结果 7", "status": "skipped", "start": 1750920808254, "stop": 1750920808254}], "start": 1750920808253, "stop": 1750920808254, "uuid": "ddbe09b4-9537-4f10-94ee-04dcda98cf39", "historyId": "b173ac1221cc8eeeace98d836653d440", "fullName": "计算器功能: 两个数字相除", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}