{"name": "不同数字的加法运算 -- @1.4 ", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937352511, "stop": 1750937352511}, {"name": "Given 我已经输入数字 -10 到计算器中", "status": "skipped", "start": 1750937352511, "stop": 1750937352511}, {"name": "And 我已经输入数字 5 到计算器中", "status": "skipped", "start": 1750937352511, "stop": 1750937352511}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750937352511, "stop": 1750937352511}, {"name": "Then 屏幕上应该显示结果 -5", "status": "skipped", "start": 1750937352511, "stop": 1750937352511}], "parameters": [{"name": "第一个数", "value": "-10"}, {"name": "第二个数", "value": "5"}, {"name": "结果", "value": "-5"}], "start": 1750937352509, "stop": 1750937352511, "uuid": "a01ff7aa-7c97-4dd7-b1ff-1905a954fbed", "historyId": "19a7a37e9a802f6db0bfeca21d41fbf0", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}