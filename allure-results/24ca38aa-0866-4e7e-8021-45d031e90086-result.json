{"name": "Adding different numbers -- @1.1 ", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "4c6d9bb4-a51a-4436-8448-91237b660818-attachment.txt", "type": "text/plain"}], "start": 1750910440573, "stop": 1750910440574}, {"name": "Given I have entered 10 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "c6ee479d-5364-4ce6-91f6-aa2899289c74-attachment.txt", "type": "text/plain"}], "start": 1750910440574, "stop": 1750910440575}, {"name": "And I have entered 20 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "ec773e9f-1539-4371-b334-77a2312a723e-attachment.txt", "type": "text/plain"}], "start": 1750910440576, "stop": 1750910440576}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "82eb61c3-a0a9-46ac-9c0a-1a2385ba5507-attachment.txt", "type": "text/plain"}], "start": 1750910440577, "stop": 1750910440578}], "start": 1750910440577, "stop": 1750910440578}, {"name": "Then the result should be 30 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 30", "status": "passed", "attachments": [{"name": "Result Verification", "source": "9e738d81-001a-40af-868e-3418ba8938f5-attachment.txt", "type": "text/plain"}], "start": 1750910440578, "stop": 1750910440579}], "start": 1750910440578, "stop": 1750910440579}], "parameters": [{"name": "first", "value": "10"}, {"name": "second", "value": "20"}, {"name": "result", "value": "30"}], "start": 1750910440571, "stop": 1750910440579, "uuid": "87a40112-5c3b-4806-8068-bf2b409fd052", "historyId": "6a60c83de3fb0bee4a30f60694eec811", "fullName": "Calculator Operations: Adding different numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}