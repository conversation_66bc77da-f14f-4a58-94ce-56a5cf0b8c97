{"name": "Calculator maintains calculation history", "status": "skipped", "steps": [{"name": "Given I have a calculator", "status": "skipped", "start": 1750910440626, "stop": 1750910440626}, {"name": "Given I have entered 10 into the calculator", "status": "skipped", "start": 1750910440626, "stop": 1750910440626}, {"name": "And I have entered 5 into the calculator", "status": "skipped", "start": 1750910440626, "stop": 1750910440626}, {"name": "When I press add", "status": "skipped", "start": 1750910440626, "stop": 1750910440626}, {"name": "Then the result should be 15 on the screen", "status": "skipped", "start": 1750910440626, "stop": 1750910440626}, {"name": "And the calculation history should contain \"10 + 5 = 15\"", "status": "skipped", "start": 1750910440626, "stop": 1750910440627}, {"name": "When I have entered 3 into the calculator", "status": "skipped", "start": 1750910440627, "stop": 1750910440627}, {"name": "And I press multiply", "status": "skipped", "start": 1750910440627, "stop": 1750910440627}, {"name": "Then the result should be 45 on the screen", "status": "skipped", "start": 1750910440627, "stop": 1750910440627}, {"name": "And the calculation history should contain \"15 * 3 = 45\"", "status": "skipped", "start": 1750910440627, "stop": 1750910440627}], "start": 1750910440625, "stop": 1750910440627, "uuid": "23e06dff-828d-4a26-ab6d-00062fef4972", "historyId": "7605ba6a4d6e760087e578640b4a5746", "fullName": "Calculator Operations: Calculator maintains calculation history", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}