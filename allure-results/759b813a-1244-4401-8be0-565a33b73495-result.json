{"name": "计算幂运算", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750923106714, "stop": 1750923106714}, {"name": "Given 我已经输入数字 2 到计算器中", "status": "skipped", "start": 1750923106714, "stop": 1750923106714}, {"name": "And 我已经输入数字 3 到计算器中", "status": "skipped", "start": 1750923106714, "stop": 1750923106714}, {"name": "When 我按下幂运算按钮", "status": "skipped", "start": 1750923106714, "stop": 1750923106714}, {"name": "Then 屏幕上应该显示结果 8", "status": "skipped", "start": 1750923106714, "stop": 1750923106714}], "start": 1750923106712, "stop": 1750923106714, "uuid": "2f2b8b03-503d-4742-9c06-a02dca92f1b6", "historyId": "c68aa1364bc6dcc719a82e99b799f3b0", "fullName": "计算器功能: 计算幂运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}