{"name": "计算幂运算", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937711840, "stop": 1750937711840}, {"name": "Given 我已经输入数字 2 到计算器中", "status": "skipped", "start": 1750937711840, "stop": 1750937711840}, {"name": "And 我已经输入数字 3 到计算器中", "status": "skipped", "start": 1750937711840, "stop": 1750937711840}, {"name": "When 我按下幂运算按钮", "status": "skipped", "start": 1750937711841, "stop": 1750937711841}, {"name": "Then 屏幕上应该显示结果 8", "status": "skipped", "start": 1750937711841, "stop": 1750937711841}], "start": 1750937711839, "stop": 1750937711841, "uuid": "1f925fc3-718c-4ed3-960a-93b880f535fd", "historyId": "c68aa1364bc6dcc719a82e99b799f3b0", "fullName": "计算器功能: 计算幂运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}