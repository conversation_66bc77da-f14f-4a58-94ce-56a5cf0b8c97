{"name": "计算器维护计算历史记录", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "And 我已经输入数字 5 到计算器中", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "Then 屏幕上应该显示结果 15", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "And 计算历史记录应该包含 \"10 + 5 = 15\"", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "When 我已经输入数字 3 到计算器中", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "And 我按下乘法按钮", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "Then 屏幕上应该显示结果 45", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}, {"name": "And 计算历史记录应该包含 \"15 * 3 = 45\"", "status": "skipped", "start": 1750920808262, "stop": 1750920808262}], "start": 1750920808261, "stop": 1750920808262, "uuid": "c90c4fd7-dd8f-43a2-af17-3b2ebc509b15", "historyId": "2212440e47c6241ce16f9a6ef923a25b", "fullName": "计算器功能: 计算器维护计算历史记录", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}