{"name": "Multiplying two numbers", "status": "skipped", "steps": [{"name": "Given I have a calculator", "status": "skipped", "start": 1750910440614, "stop": 1750910440614}, {"name": "Given I have entered 6 into the calculator", "status": "skipped", "start": 1750910440614, "stop": 1750910440614}, {"name": "And I have entered 7 into the calculator", "status": "skipped", "start": 1750910440614, "stop": 1750910440614}, {"name": "When I press multiply", "status": "skipped", "start": 1750910440614, "stop": 1750910440614}, {"name": "Then the result should be 42 on the screen", "status": "skipped", "start": 1750910440614, "stop": 1750910440614}], "start": 1750910440613, "stop": 1750910440614, "uuid": "11b097bc-064f-42ae-abd1-a0fc1a1cce69", "historyId": "c8e6447541b43bc24b5e60092c7fefbb", "fullName": "Calculator Operations: Multiplying two numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}