{"name": "不同数字的加法运算 -- @1.1 ", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750921317461, "stop": 1750921317461}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750921317461, "stop": 1750921317461}, {"name": "And 我已经输入数字 20 到计算器中", "status": "skipped", "start": 1750921317461, "stop": 1750921317461}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750921317461, "stop": 1750921317461}, {"name": "Then 屏幕上应该显示结果 30", "status": "skipped", "start": 1750921317461, "stop": 1750921317461}], "parameters": [{"name": "第一个数", "value": "10"}, {"name": "第二个数", "value": "20"}, {"name": "结果", "value": "30"}], "start": 1750921317460, "stop": 1750921317461, "uuid": "bab69fa7-9610-43ba-9089-ec303373272a", "historyId": "d13f8d4ff0120894e90927ccbaa9a3df", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}