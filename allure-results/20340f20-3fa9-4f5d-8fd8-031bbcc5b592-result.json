{"name": "两个正数相加", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750991492292, "stop": 1750991492293}, {"name": "Given 我已经输入数字 50 到计算器中", "status": "skipped", "start": 1750991492293, "stop": 1750991492293}, {"name": "And 我已经输入数字 70 到计算器中", "status": "skipped", "start": 1750991492293, "stop": 1750991492293}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750991492293, "stop": 1750991492293}, {"name": "Then 屏幕上应该显示结果 120", "status": "skipped", "start": 1750991492293, "stop": 1750991492293}], "start": 1750991492291, "stop": 1750991492293, "uuid": "d4290523-4f04-4ce9-bbb3-9e74e53d5ee2", "historyId": "bb528dd71bbfcb703aa589df11393955", "fullName": "计算器功能: 两个正数相加", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}