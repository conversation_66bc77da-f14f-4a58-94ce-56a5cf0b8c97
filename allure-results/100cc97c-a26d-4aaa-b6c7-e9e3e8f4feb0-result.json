{"name": "计算幂运算", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "e1715fff-2f4f-4701-b555-f83773cf11f9-attachment.txt", "type": "text/plain"}], "start": 1750918797910, "stop": 1750918797912}, {"name": "Given 我已经输入数字 2 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "8d60f2e3-3ce4-4b6a-8119-a332c590c146-attachment.txt", "type": "text/plain"}], "start": 1750918797912, "stop": 1750918797913}, {"name": "And 我已经输入数字 3 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "bc33c674-cce0-49b7-973b-95dfc2f9ff73-attachment.txt", "type": "text/plain"}], "start": 1750918797914, "stop": 1750918797915}, {"name": "When 我按下幂运算按钮", "status": "passed", "steps": [{"name": "Performing power calculation", "status": "passed", "attachments": [{"name": "Operation", "source": "f660d9ec-1fbd-4d31-875f-f36697b09246-attachment.txt", "type": "text/plain"}], "start": 1750918797923, "stop": 1750918797925}], "start": 1750918797919, "stop": 1750918797925}, {"name": "Then 屏幕上应该显示结果 8", "status": "passed", "steps": [{"name": "Verifying result equals 8", "status": "passed", "attachments": [{"name": "Result Verification", "source": "8142855a-a8ae-4fc1-bbbb-81178f502804-attachment.txt", "type": "text/plain"}], "start": 1750918797926, "stop": 1750918797928}], "start": 1750918797926, "stop": 1750918797928}], "start": 1750918797909, "stop": 1750918797928, "uuid": "47faa718-1a87-476b-922a-f6282a625a3d", "historyId": "c68aa1364bc6dcc719a82e99b799f3b0", "fullName": "计算器功能: 计算幂运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}