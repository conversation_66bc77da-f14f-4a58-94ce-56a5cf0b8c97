{"name": "两个数字相乘", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750921317470, "stop": 1750921317470}, {"name": "Given 我已经输入数字 6 到计算器中", "status": "skipped", "start": 1750921317471, "stop": 1750921317471}, {"name": "And 我已经输入数字 7 到计算器中", "status": "skipped", "start": 1750921317471, "stop": 1750921317471}, {"name": "When 我按下乘法按钮", "status": "skipped", "start": 1750921317471, "stop": 1750921317471}, {"name": "Then 屏幕上应该显示结果 42", "status": "skipped", "start": 1750921317471, "stop": 1750921317471}], "start": 1750921317470, "stop": 1750921317471, "uuid": "0b6793b6-cb85-4445-9291-34d52c9c7cc2", "historyId": "f3662b2d33db3b7b0965a67f9fdce563", "fullName": "计算器功能: 两个数字相乘", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}