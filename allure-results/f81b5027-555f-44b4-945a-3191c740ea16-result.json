{"name": "Adding different numbers -- @1.4 ", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "958547b1-b38f-4ea0-89af-cfd35eaa4b49-attachment.txt", "type": "text/plain"}], "start": 1750910440602, "stop": 1750910440603}, {"name": "Given I have entered -10 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "f69ed9e4-43bb-40c7-83a5-02ec3eb3c735-attachment.txt", "type": "text/plain"}], "start": 1750910440604, "stop": 1750910440604}, {"name": "And I have entered 5 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "88e833a1-bbf3-4aff-99a0-2500f5d63fd2-attachment.txt", "type": "text/plain"}], "start": 1750910440605, "stop": 1750910440606}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "6167615b-b308-4d50-b1d9-ef927ea7ca3c-attachment.txt", "type": "text/plain"}], "start": 1750910440607, "stop": 1750910440608}], "start": 1750910440607, "stop": 1750910440608}, {"name": "Then the result should be -5 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals -5", "status": "passed", "attachments": [{"name": "Result Verification", "source": "b0dcb8aa-75f0-49ac-a174-60ea422118d7-attachment.txt", "type": "text/plain"}], "start": 1750910440608, "stop": 1750910440609}], "start": 1750910440608, "stop": 1750910440609}], "parameters": [{"name": "first", "value": "-10"}, {"name": "second", "value": "5"}, {"name": "result", "value": "-5"}], "start": 1750910440600, "stop": 1750910440609, "uuid": "e7c45171-6894-495e-a506-d872a4def846", "historyId": "7a0a865e0e4954d9fc2804c1a09bdd33", "fullName": "Calculator Operations: Adding different numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}