{"name": "Calculating power", "status": "skipped", "steps": [{"name": "Given I have a calculator", "status": "skipped", "start": 1750910440623, "stop": 1750910440623}, {"name": "Given I have entered 2 into the calculator", "status": "skipped", "start": 1750910440624, "stop": 1750910440624}, {"name": "And I have entered 3 into the calculator", "status": "skipped", "start": 1750910440624, "stop": 1750910440624}, {"name": "When I press power", "status": "skipped", "start": 1750910440624, "stop": 1750910440624}, {"name": "Then the result should be 8 on the screen", "status": "skipped", "start": 1750910440624, "stop": 1750910440624}], "start": 1750910440621, "stop": 1750910440624, "uuid": "ec4eb1b6-25f1-4a4d-8360-b68113deae8a", "historyId": "1bc50e1c959a567c5fd3b555837e554a", "fullName": "Calculator Operations: Calculating power", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}