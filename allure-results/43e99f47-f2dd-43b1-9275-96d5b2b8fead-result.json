{"name": "Adding different numbers -- @1.3 ", "status": "passed", "steps": [{"name": "Given I have a calculator", "status": "passed", "attachments": [{"name": "Setup", "source": "5627a92a-f650-4d24-ae97-667ed5055b72-attachment.txt", "type": "text/plain"}], "start": 1750910677356, "stop": 1750910677357}, {"name": "Given I have entered 0 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "c562b10c-b8fa-4f78-a2f6-a54a26177668-attachment.txt", "type": "text/plain"}], "start": 1750910677357, "stop": 1750910677358}, {"name": "And I have entered 100 into the calculator", "status": "passed", "attachments": [{"name": "Input", "source": "20d26207-52fa-43b9-938d-df3e30decc10-attachment.txt", "type": "text/plain"}], "start": 1750910677358, "stop": 1750910677358}, {"name": "When I press add", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "d1d89b08-4379-44f0-b35b-030f20276cc8-attachment.txt", "type": "text/plain"}], "start": 1750910677359, "stop": 1750910677359}], "start": 1750910677359, "stop": 1750910677359}, {"name": "Then the result should be 100 on the screen", "status": "passed", "steps": [{"name": "Verifying result equals 100", "status": "passed", "attachments": [{"name": "Result Verification", "source": "3bfa5c56-64f3-4723-859c-8a31456ffdc1-attachment.txt", "type": "text/plain"}], "start": 1750910677360, "stop": 1750910677360}], "start": 1750910677360, "stop": 1750910677360}], "parameters": [{"name": "first", "value": "0"}, {"name": "second", "value": "100"}, {"name": "result", "value": "100"}], "start": 1750910677355, "stop": 1750910677360, "uuid": "7a527350-db0d-4284-8a5d-36dd568f872d", "historyId": "0ec47a2af2b5dc4d5425cfbe164d1849", "fullName": "Calculator Operations: Adding different numbers", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}