{"name": "测试报告书写", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "d191a68e-265d-4570-99b9-6290fac61a4c-attachment.txt", "type": "text/plain"}], "start": 1750923106724, "stop": 1750923106726}, {"name": "Given given: 测试111", "status": "passed", "start": 1750923106726, "stop": 1750923106728}, {"name": "When when: 测试111", "status": "passed", "start": 1750923106728, "stop": 1750923106731}, {"name": "Then then: 测试111", "status": "passed", "start": 1750923106731, "stop": 1750923106733}], "start": 1750923106721, "stop": 1750923106733, "uuid": "387f9dcd-2630-452a-bafa-944cf5707845", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}