{"name": "测试报告书写", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "38f83ef7-42de-4f23-90e7-637d9a5c6142-attachment.txt", "type": "text/plain"}], "start": 1750937791522, "stop": 1750937791523}, {"name": "Given given: 测试111", "status": "passed", "start": 1750937791523, "stop": 1750937791525}, {"name": "When when: 测试111", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "start": 1750937791525, "stop": 1750937791527}, {"name": "Then then: 测试111", "status": "skipped", "start": 1750937791528, "stop": 1750937791528}], "attachments": [{"name": "Log Summary - 测试报告书写", "source": "226d3f6a-9e6b-434a-8bab-1b9860a59ea9-attachment.txt", "type": "text/plain"}], "start": 1750937791520, "stop": 1750937791528, "uuid": "96790783-cfb5-4ff6-a4f8-b70588e0b4f7", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}