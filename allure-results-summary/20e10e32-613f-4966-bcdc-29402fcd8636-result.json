{"name": "不同数字的加法运算 -- @1.3 ", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937791500, "stop": 1750937791500}, {"name": "Given 我已经输入数字 0 到计算器中", "status": "skipped", "start": 1750937791500, "stop": 1750937791500}, {"name": "And 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750937791500, "stop": 1750937791500}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750937791500, "stop": 1750937791500}, {"name": "Then 屏幕上应该显示结果 100", "status": "skipped", "start": 1750937791500, "stop": 1750937791500}], "parameters": [{"name": "第一个数", "value": "0"}, {"name": "第二个数", "value": "100"}, {"name": "结果", "value": "100"}], "start": 1750937791499, "stop": 1750937791500, "uuid": "2916dc63-fd6b-4093-b3ed-1d1b568b6f5d", "historyId": "17d7009b30795a79145207c83df82232", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}