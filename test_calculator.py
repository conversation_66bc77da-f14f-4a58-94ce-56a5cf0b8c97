#!/usr/bin/env python3
"""
Simple test to verify calculator functionality before running behave tests
"""

from calculator import Calculator


def test_calculator_basic():
    """Test basic calculator functionality"""
    calc = Calculator()
    
    # Test addition
    result = calc.add(10, 5)
    assert result == 15, f"Expected 15, got {result}"
    print("✓ Addition test passed")
    
    # Test subtraction
    result = calc.subtract(10, 3)
    assert result == 7, f"Expected 7, got {result}"
    print("✓ Subtraction test passed")
    
    # Test multiplication
    result = calc.multiply(6, 7)
    assert result == 42, f"Expected 42, got {result}"
    print("✓ Multiplication test passed")
    
    # Test division
    result = calc.divide(84, 12)
    assert result == 7, f"Expected 7, got {result}"
    print("✓ Division test passed")
    
    # Test power
    result = calc.power(2, 3)
    assert result == 8, f"Expected 8, got {result}"
    print("✓ Power test passed")
    
    # Test division by zero
    try:
        calc.divide(10, 0)
        assert False, "Should have raised ValueError"
    except ValueError as e:
        assert str(e) == "Cannot divide by zero"
        print("✓ Division by zero error test passed")
    
    # Test history
    history = calc.get_history()
    assert len(history) > 0, "History should not be empty"
    print(f"✓ History test passed: {len(history)} operations recorded")
    
    print("\nAll calculator tests passed! ✅")


if __name__ == "__main__":
    test_calculator_basic()
