{"name": "不同数字的加法运算 -- @1.1 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "c451d5c3-b677-46d1-b404-a73d368449b6-attachment.txt", "type": "text/plain"}], "start": 1750911091361, "stop": 1750911091363}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "af1b17a1-eeeb-44c1-a75f-d061acb50a2a-attachment.txt", "type": "text/plain"}], "start": 1750911091363, "stop": 1750911091364}, {"name": "And 我已经输入数字 20 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "1b3424f3-b581-498a-bfcc-9ad780299462-attachment.txt", "type": "text/plain"}], "start": 1750911091365, "stop": 1750911091366}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "3f330fc7-64a4-4b81-87e5-47761bc0327b-attachment.txt", "type": "text/plain"}], "start": 1750911091367, "stop": 1750911091367}], "start": 1750911091366, "stop": 1750911091367}, {"name": "Then 屏幕上应该显示结果 30", "status": "passed", "steps": [{"name": "Verifying result equals 30", "status": "passed", "attachments": [{"name": "Result Verification", "source": "add28990-211d-4939-aebc-cd9fa4742475-attachment.txt", "type": "text/plain"}], "start": 1750911091368, "stop": 1750911091368}], "start": 1750911091368, "stop": 1750911091368}], "parameters": [{"name": "第一个数", "value": "10"}, {"name": "第二个数", "value": "20"}, {"name": "结果", "value": "30"}], "start": 1750911091360, "stop": 1750911091368, "uuid": "ec4026b8-0b6b-4cbb-8bf3-98d567980e2b", "historyId": "d13f8d4ff0120894e90927ccbaa9a3df", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}