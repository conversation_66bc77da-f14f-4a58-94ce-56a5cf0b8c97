{"name": "不同数字的加法运算 -- @1.2 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "4a76a1ca-8d87-4b0b-bf09-bc7350e12b03-attachment.txt", "type": "text/plain"}], "start": 1750911091370, "stop": 1750911091371}, {"name": "Given 我已经输入数字 5 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "2c0e7817-3136-4a82-b749-3fbc80fe6748-attachment.txt", "type": "text/plain"}], "start": 1750911091371, "stop": 1750911091372}, {"name": "And 我已经输入数字 15 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "9bc3082e-0c4b-4c67-b1f5-f995a81b262f-attachment.txt", "type": "text/plain"}], "start": 1750911091373, "stop": 1750911091374}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "2cffe648-60fc-4e56-b194-40601378788b-attachment.txt", "type": "text/plain"}], "start": 1750911091374, "stop": 1750911091375}], "start": 1750911091374, "stop": 1750911091375}, {"name": "Then 屏幕上应该显示结果 20", "status": "passed", "steps": [{"name": "Verifying result equals 20", "status": "passed", "attachments": [{"name": "Result Verification", "source": "de3cb7e1-b959-4ea7-bd4b-794893f66525-attachment.txt", "type": "text/plain"}], "start": 1750911091375, "stop": 1750911091376}], "start": 1750911091375, "stop": 1750911091376}], "parameters": [{"name": "第一个数", "value": "5"}, {"name": "第二个数", "value": "15"}, {"name": "结果", "value": "20"}], "start": 1750911091369, "stop": 1750911091376, "uuid": "49ea5353-ec68-47bb-bef7-1b57df39102b", "historyId": "467e476b619440f9346e9606f609a30c", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}