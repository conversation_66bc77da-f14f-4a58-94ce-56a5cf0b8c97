{"name": "计算幂运算", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750911091403, "stop": 1750911091403}, {"name": "Given 我已经输入数字 2 到计算器中", "status": "skipped", "start": 1750911091403, "stop": 1750911091403}, {"name": "And 我已经输入数字 3 到计算器中", "status": "skipped", "start": 1750911091404, "stop": 1750911091404}, {"name": "When 我按下幂运算按钮", "status": "skipped", "start": 1750911091404, "stop": 1750911091404}, {"name": "Then 屏幕上应该显示结果 8", "status": "skipped", "start": 1750911091404, "stop": 1750911091404}], "start": 1750911091402, "stop": 1750911091404, "uuid": "e8649e41-3a17-4211-ab1a-3aa8162f7b1e", "historyId": "c68aa1364bc6dcc719a82e99b799f3b0", "fullName": "计算器功能: 计算幂运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}