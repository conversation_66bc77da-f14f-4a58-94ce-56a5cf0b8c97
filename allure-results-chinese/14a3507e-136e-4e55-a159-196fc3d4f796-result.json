{"name": "两个正数相加", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "23190ea7-7a8a-4907-adb1-1ebe94ff4e12-attachment.txt", "type": "text/plain"}], "start": 1750911091351, "stop": 1750911091352}, {"name": "Given 我已经输入数字 50 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "4c84cd37-7823-437a-8d82-eeca63374cf4-attachment.txt", "type": "text/plain"}], "start": 1750911091353, "stop": 1750911091353}, {"name": "And 我已经输入数字 70 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "260eba1a-3a89-4eed-b69d-73213f70994c-attachment.txt", "type": "text/plain"}], "start": 1750911091354, "stop": 1750911091355}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "be886008-c32e-4222-9d62-8b045238a2d1-attachment.txt", "type": "text/plain"}], "start": 1750911091356, "stop": 1750911091356}], "start": 1750911091355, "stop": 1750911091357}, {"name": "Then 屏幕上应该显示结果 120", "status": "passed", "steps": [{"name": "Verifying result equals 120", "status": "passed", "attachments": [{"name": "Result Verification", "source": "80856d05-e90d-4a93-acf6-5cf24db7e18f-attachment.txt", "type": "text/plain"}], "start": 1750911091357, "stop": 1750911091358}], "start": 1750911091357, "stop": 1750911091358}], "start": 1750911091349, "stop": 1750911091358, "uuid": "61378eb6-0fec-4e1a-973f-7980193006c4", "historyId": "bb528dd71bbfcb703aa589df11393955", "fullName": "计算器功能: 两个正数相加", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}