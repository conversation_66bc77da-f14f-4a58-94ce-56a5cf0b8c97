{"name": "两个数字相除", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750911091399, "stop": 1750911091399}, {"name": "Given 我已经输入数字 84 到计算器中", "status": "skipped", "start": 1750911091399, "stop": 1750911091399}, {"name": "And 我已经输入数字 12 到计算器中", "status": "skipped", "start": 1750911091399, "stop": 1750911091399}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750911091399, "stop": 1750911091399}, {"name": "Then 屏幕上应该显示结果 7", "status": "skipped", "start": 1750911091399, "stop": 1750911091399}], "start": 1750911091396, "stop": 1750911091399, "uuid": "4335fc26-9964-476f-ae20-b7576eba6e5b", "historyId": "b173ac1221cc8eeeace98d836653d440", "fullName": "计算器功能: 两个数字相除", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}