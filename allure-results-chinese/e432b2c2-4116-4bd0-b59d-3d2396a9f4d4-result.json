{"name": "不同数字的加法运算 -- @1.3 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "606095d2-7c79-41ad-ad9c-f14b36cfb914-attachment.txt", "type": "text/plain"}], "start": 1750911091379, "stop": 1750911091379}, {"name": "Given 我已经输入数字 0 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "ae5ec8e9-3bc4-4cae-81e3-21591e806e20-attachment.txt", "type": "text/plain"}], "start": 1750911091379, "stop": 1750911091381}, {"name": "And 我已经输入数字 100 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "04973eee-ee6b-4d1b-ba46-8cca808dd03f-attachment.txt", "type": "text/plain"}], "start": 1750911091382, "stop": 1750911091383}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "4e1484f6-5aa4-460b-8b0d-1fb688158b94-attachment.txt", "type": "text/plain"}], "start": 1750911091383, "stop": 1750911091384}], "start": 1750911091383, "stop": 1750911091384}, {"name": "Then 屏幕上应该显示结果 100", "status": "passed", "steps": [{"name": "Verifying result equals 100", "status": "passed", "attachments": [{"name": "Result Verification", "source": "acfd62bc-89f3-4c5b-925b-7645ef274e32-attachment.txt", "type": "text/plain"}], "start": 1750911091384, "stop": 1750911091385}], "start": 1750911091384, "stop": 1750911091385}], "parameters": [{"name": "第一个数", "value": "0"}, {"name": "第二个数", "value": "100"}, {"name": "结果", "value": "100"}], "start": 1750911091377, "stop": 1750911091385, "uuid": "a89526c6-d65f-43a8-b36b-1c63bf0cad6d", "historyId": "17d7009b30795a79145207c83df82232", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}