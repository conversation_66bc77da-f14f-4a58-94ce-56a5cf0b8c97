{"name": "不同数字的加法运算 -- @1.4 ", "status": "passed", "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "ddbb9112-0f63-4d97-acef-58e4ccc053b6-attachment.txt", "type": "text/plain"}], "start": 1750911091388, "stop": 1750911091389}, {"name": "Given 我已经输入数字 -10 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "b5a6f54a-12ab-48eb-8383-d7a533a3fc3c-attachment.txt", "type": "text/plain"}], "start": 1750911091389, "stop": 1750911091390}, {"name": "And 我已经输入数字 5 到计算器中", "status": "passed", "attachments": [{"name": "Input", "source": "e57a1935-e762-476a-8eff-fc7f3a44f0d2-attachment.txt", "type": "text/plain"}], "start": 1750911091390, "stop": 1750911091390}, {"name": "When 我按下加法按钮", "status": "passed", "steps": [{"name": "Performing addition", "status": "passed", "attachments": [{"name": "Operation", "source": "5db5276f-5f9d-4a7b-8e0b-f1939d9ac8ee-attachment.txt", "type": "text/plain"}], "start": 1750911091391, "stop": 1750911091391}], "start": 1750911091390, "stop": 1750911091391}, {"name": "Then 屏幕上应该显示结果 -5", "status": "passed", "steps": [{"name": "Verifying result equals -5", "status": "passed", "attachments": [{"name": "Result Verification", "source": "fdf22912-bec5-4cf7-9c9a-d8546d66e7d3-attachment.txt", "type": "text/plain"}], "start": 1750911091391, "stop": 1750911091392}], "start": 1750911091391, "stop": 1750911091392}], "parameters": [{"name": "第一个数", "value": "-10"}, {"name": "第二个数", "value": "5"}, {"name": "结果", "value": "-5"}], "start": 1750911091387, "stop": 1750911091392, "uuid": "0351b41e-6e97-4b4b-a154-2fd207967f77", "historyId": "19a7a37e9a802f6db0bfeca21d41fbf0", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}