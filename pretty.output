Feature: 计算器功能 # features/calculator.feature:1
  作为一个用户
  我想要执行基本的数学运算
  以便我能够准确地计算结果
  Background:   # features/calculator.feature:6

  @smoke @addition
  Scenario: 两个正数相加         # features/calculator.feature:10
    Given 我有一个计算器          # None
    Given 我已经输入数字 50 到计算器中 # None
    And 我已经输入数字 70 到计算器中   # None
    When 我按下加法按钮           # None
    Then 屏幕上应该显示结果 120     # None

  @smoke @addition
  Scenario Outline: 不同数字的加法运算 -- @1.1   # features/calculator.feature:25
    Given 我有一个计算器                       # None
    Given 我已经输入数字 10 到计算器中              # None
    And 我已经输入数字 20 到计算器中                # None
    When 我按下加法按钮                        # None
    Then 屏幕上应该显示结果 30                   # None

  @smoke @addition
  Scenario Outline: 不同数字的加法运算 -- @1.2   # features/calculator.feature:26
    Given 我有一个计算器                       # None
    Given 我已经输入数字 5 到计算器中               # None
    And 我已经输入数字 15 到计算器中                # None
    When 我按下加法按钮                        # None
    Then 屏幕上应该显示结果 20                   # None

  @smoke @addition
  Scenario Outline: 不同数字的加法运算 -- @1.3   # features/calculator.feature:27
    Given 我有一个计算器                       # None
    Given 我已经输入数字 0 到计算器中               # None
    And 我已经输入数字 100 到计算器中               # None
    When 我按下加法按钮                        # None
    Then 屏幕上应该显示结果 100                  # None

  @smoke @addition
  Scenario Outline: 不同数字的加法运算 -- @1.4   # features/calculator.feature:28
    Given 我有一个计算器                       # None
    Given 我已经输入数字 -10 到计算器中             # None
    And 我已经输入数字 5 到计算器中                 # None
    When 我按下加法按钮                        # None
    Then 屏幕上应该显示结果 -5                   # None

  @subtraction
  Scenario: 两个数字相减          # features/calculator.feature:31
    Given 我有一个计算器           # None
    Given 我已经输入数字 100 到计算器中 # None
    And 我已经输入数字 30 到计算器中    # None
    When 我按下减法按钮            # None
    Then 屏幕上应该显示结果 70       # None

  @multiplication
  Scenario: 两个数字相乘        # features/calculator.feature:38
    Given 我有一个计算器         # None
    Given 我已经输入数字 6 到计算器中 # None
    And 我已经输入数字 7 到计算器中   # None
    When 我按下乘法按钮          # None
    Then 屏幕上应该显示结果 42     # None

  @division
  Scenario: 两个数字相除         # features/calculator.feature:45
    Given 我有一个计算器          # None
    Given 我已经输入数字 84 到计算器中 # None
    And 我已经输入数字 12 到计算器中   # None
    When 我按下除法按钮           # None
    Then 屏幕上应该显示结果 7       # None

  @division @error
  Scenario: 除零应该抛出错误                       # features/calculator.feature:52
    Given 我有一个计算器                          # None
    Given 我已经输入数字 10 到计算器中                 # None
    And 我已经输入数字 0 到计算器中                    # None
    When 我按下除法按钮                           # None
    Then 我应该看到错误信息 "Cannot divide by zero" # None

  @power
  Scenario: 计算幂运算         # features/calculator.feature:59
    Given 我有一个计算器         # None
    Given 我已经输入数字 2 到计算器中 # None
    And 我已经输入数字 3 到计算器中   # None
    When 我按下幂运算按钮         # None
    Then 屏幕上应该显示结果 8      # None

  @history
  Scenario: 计算器维护计算历史记录          # features/calculator.feature:66
    Given 我有一个计算器                # None
    Given 我已经输入数字 10 到计算器中       # None
    And 我已经输入数字 5 到计算器中          # None
    When 我按下加法按钮                 # None
    Then 屏幕上应该显示结果 15            # None
    And 计算历史记录应该包含 "10 + 5 = 15" # None
    When 我已经输入数字 3 到计算器中         # None
    And 我按下乘法按钮                  # None
    Then 屏幕上应该显示结果 45            # None
    And 计算历史记录应该包含 "15 * 3 = 45" # None

  @test
  Scenario: 测试报告书写     # features/calculator.feature:78
    Given 我有一个计算器      # features/steps/calculator_steps.py:63
    Given given: 测试111 # features/steps/calculator_steps.py:22
    When when: 测试111   # features/steps/calculator_steps.py:11
      Traceback (most recent call last):
        File "/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py", line 1329, in run
          match.run(runner.context)
        File "/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py", line 98, in run
          self.func(context, *args, **kwargs)
        File "features/steps/calculator_steps.py", line 17, in step_when_test
          raise Exception("测试1111")
      Exception: 测试1111

    Then then: 测试111   # None

