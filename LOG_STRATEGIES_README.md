# 日志策略优化方案

## 问题描述

在使用 behave-allure 时，如果每个日志条目都作为单独的附件添加到 Allure 报告中，会导致报告变得非常臃肿，影响可读性和性能。

## 解决方案

我们提供了5种不同的日志策略来优化 Allure 报告中的日志展示：

### 1. 缓冲策略 (Buffered Strategy) - **推荐**

**特点**: 收集整个场景的所有日志，在场景结束时作为一个附件添加
**优点**: 
- 每个场景只有一个日志附件
- 保留完整的日志信息
- 时间戳清晰，便于调试

**适用场景**: 大多数情况下的首选方案

```python
# 使用方式
export LOG_STRATEGY=buffered
behave -f allure_behave.formatter:AllureFormatter -o allure-results
```

### 2. 级别分组策略 (Level Grouped Strategy)

**特点**: 按日志级别（INFO、WARNING、ERROR）分别收集和附加
**优点**: 
- 可以快速定位特定级别的日志
- 错误日志单独展示，便于问题排查

**适用场景**: 需要按日志级别快速筛选的场景

### 3. 步骤分组策略 (Step Grouped Strategy)

**特点**: 每个测试步骤的日志单独收集和附加
**优点**: 
- 可以精确定位哪个步骤产生了哪些日志
- 便于步骤级别的调试

**适用场景**: 复杂的多步骤测试场景

### 4. 阈值策略 (Threshold Strategy)

**特点**: 只附加重要日志（WARNING、ERROR）和失败场景的所有日志
**优点**: 
- 大幅减少附件数量
- 重点关注问题和错误
- 成功场景的报告非常简洁

**适用场景**: 生产环境或大量测试场景

### 5. 摘要策略 (Summary Strategy)

**特点**: 只附加日志统计摘要和少量示例
**优点**: 
- 附件数量最少
- 提供日志概览信息
- 报告最简洁

**适用场景**: 只需要了解日志概况的场景

## 使用方法

### 方法1: 使用演示脚本（推荐）

```bash
python demo_log_strategies.py
```

这个脚本会让您选择不同的策略并自动运行测试，生成对比报告。

### 方法2: 手动切换策略

1. 备份当前环境文件：
```bash
cp features/environment.py features/environment_backup.py
```

2. 使用优化的环境文件：
```bash
cp features/environment_optimized.py features/environment.py
```

3. 设置策略并运行测试：
```bash
export LOG_STRATEGY=buffered  # 或其他策略名称
behave -f allure_behave.formatter:AllureFormatter -o allure-results
```

4. 查看报告：
```bash
allure serve allure-results
```

### 方法3: 直接修改代码

在 `features/environment_optimized.py` 中修改这一行：
```python
LOG_STRATEGY = os.getenv('LOG_STRATEGY', 'buffered')  # 改为您想要的策略
```

## 策略对比

| 策略 | 附件数量 | 信息完整性 | 可读性 | 性能 | 推荐场景 |
|------|----------|------------|--------|------|----------|
| buffered | 低 | 高 | 高 | 好 | 通用 |
| level_grouped | 中 | 高 | 中 | 中 | 需要按级别筛选 |
| step_grouped | 中-高 | 高 | 中 | 中 | 复杂步骤调试 |
| threshold | 很低 | 中 | 高 | 很好 | 生产环境 |
| summary | 最低 | 低 | 中 | 最好 | 概览需求 |

## 配置选项

您可以通过环境变量配置策略：

```bash
# 可用的策略名称
export LOG_STRATEGY=buffered        # 缓冲策略（默认）
export LOG_STRATEGY=level_grouped   # 级别分组策略
export LOG_STRATEGY=step_grouped    # 步骤分组策略
export LOG_STRATEGY=threshold       # 阈值策略
export LOG_STRATEGY=summary         # 摘要策略
```

## 自定义策略

您也可以基于 `LogStrategy` 基类创建自己的策略：

```python
from features.log_strategies import LogStrategy

class CustomLogStrategy(LogStrategy):
    def setup(self):
        # 初始化设置
        pass
        
    def log_message(self, level: str, message: str, timestamp: datetime = None):
        # 处理日志消息
        pass
        
    def flush_logs(self, scenario_name: str = None):
        # 输出日志到 Allure
        pass
```

## 注意事项

1. **性能考虑**: 缓冲策略在内存中保存日志，对于大量日志的场景要注意内存使用
2. **调试需求**: 如果需要详细的调试信息，建议使用缓冲策略或级别分组策略
3. **生产环境**: 建议使用阈值策略或摘要策略以减少报告大小
4. **兼容性**: 所有策略都兼容现有的 loguru 日志调用，无需修改测试代码

## 示例输出

使用缓冲策略的 Allure 附件示例：
```
Scenario Logs - 测试加法运算
[14:30:15.123] INFO: 测试111111
[14:30:15.124] INFO: 测试111111
[14:30:15.125] INFO: 测试111111
[14:30:15.126] ERROR: 测试1111
```

使用摘要策略的 Allure 附件示例：
```
Log Summary for 测试加法运算
==================================================
INFO: 3 messages
  Sample INFO messages:
    [14:30:15.123] 测试111111
    [14:30:15.124] 测试111111
    [14:30:15.125] 测试111111

ERROR: 1 messages
  Sample ERROR messages:
    [14:30:15.126] 测试1111
```
