# 缓冲日志策略

## 问题描述

在使用 behave-allure 时，如果每个日志条目都作为单独的附件添加到 Allure 报告中，会导致报告变得非常臃肿，影响可读性和性能。

## 解决方案

使用缓冲策略来优化 Allure 报告中的日志展示：

### 缓冲策略 (Buffered Strategy)

**特点**: 收集整个场景的所有日志，在场景结束时作为一个附件添加

**优点**:
- ✅ 每个场景只有一个日志附件
- ✅ 保留完整的日志信息
- ✅ 时间戳清晰，便于调试
- ✅ 大幅减少附件数量
- ✅ 性能良好

**效果对比**:
- **原始方式**: 7个日志 = 7个附件 ❌
- **缓冲策略**: 7个日志 = 1个附件 ✅

## 使用方法

### 快速启用缓冲策略

1. 备份当前环境文件：
```bash
cp features/environment.py features/environment_backup.py
```

2. 使用优化的环境文件：
```bash
cp features/environment_optimized.py features/environment.py
```

3. 运行测试：
```bash
behave -f allure_behave.formatter:AllureFormatter -o allure-results
```

4. 查看报告：
```bash
allure serve allure-results
```

## 核心文件

1. **`features/log_strategies.py`** - 缓冲日志策略的实现
2. **`features/environment_optimized.py`** - 优化的环境配置

## 注意事项

1. **性能考虑**: 缓冲策略在内存中保存日志，对于大量日志的场景要注意内存使用
2. **兼容性**: 缓冲策略兼容现有的 loguru 日志调用，无需修改测试代码
3. **调试友好**: 保留完整的日志信息和时间戳，便于问题排查

## 示例输出

使用缓冲策略的 Allure 附件示例：
```
Scenario Logs - 测试报告书写
[19:36:07.676] INFO: 测试111111
[19:36:07.676] INFO: 测试111111
[19:36:07.676] INFO: 测试111111
[19:36:07.676] INFO: 测试111111
[19:36:07.677] INFO: 测试111111
[19:36:07.678] INFO: 测试111111
[19:36:07.678] INFO: 测试111111
```

**效果**: 7个日志条目合并为1个清晰的附件，大大减少了报告的臃肿程度！
