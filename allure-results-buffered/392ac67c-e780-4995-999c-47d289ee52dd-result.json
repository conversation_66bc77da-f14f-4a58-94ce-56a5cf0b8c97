{"name": "除零应该抛出错误", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937767666, "stop": 1750937767666}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750937767666, "stop": 1750937767666}, {"name": "And 我已经输入数字 0 到计算器中", "status": "skipped", "start": 1750937767666, "stop": 1750937767666}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750937767666, "stop": 1750937767666}, {"name": "Then 我应该看到错误信息 \"Cannot divide by zero\"", "status": "skipped", "start": 1750937767666, "stop": 1750937767666}], "start": 1750937767665, "stop": 1750937767666, "uuid": "57bda0c8-e77b-4dd6-b1c1-18b5375abc9d", "historyId": "038434e4c66ac0b41d9341671e2a4539", "fullName": "计算器功能: 除零应该抛出错误", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}