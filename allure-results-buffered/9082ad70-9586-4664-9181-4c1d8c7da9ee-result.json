{"name": "测试报告书写", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "a6b4351e-a88d-46dd-9070-505d1e71325e-attachment.txt", "type": "text/plain"}], "start": 1750937767675, "stop": 1750937767676}, {"name": "Given given: 测试111", "status": "passed", "start": 1750937767676, "stop": 1750937767677}, {"name": "When when: 测试111", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "start": 1750937767678, "stop": 1750937767681}, {"name": "Then then: 测试111", "status": "skipped", "start": 1750937767682, "stop": 1750937767682}], "attachments": [{"name": "Scenario Logs - 测试报告书写", "source": "570dc7f4-34ca-4e90-85d7-5ee7e024a8a4-attachment.txt", "type": "text/plain"}], "start": 1750937767673, "stop": 1750937767682, "uuid": "fb9f9f98-6996-417f-9090-b9a51d0f4db6", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}