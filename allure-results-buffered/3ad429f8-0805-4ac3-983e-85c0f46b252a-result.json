{"name": "两个正数相加", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937767645, "stop": 1750937767645}, {"name": "Given 我已经输入数字 50 到计算器中", "status": "skipped", "start": 1750937767645, "stop": 1750937767645}, {"name": "And 我已经输入数字 70 到计算器中", "status": "skipped", "start": 1750937767645, "stop": 1750937767645}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750937767645, "stop": 1750937767645}, {"name": "Then 屏幕上应该显示结果 120", "status": "skipped", "start": 1750937767645, "stop": 1750937767645}], "start": 1750937767644, "stop": 1750937767645, "uuid": "30378477-0c9e-41ac-921a-98183f080757", "historyId": "bb528dd71bbfcb703aa589df11393955", "fullName": "计算器功能: 两个正数相加", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}