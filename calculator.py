"""
Simple Calculator class for demonstrating behave-allure testing
"""


class Calculator:
    """A simple calculator class with basic arithmetic operations"""
    
    def __init__(self):
        self.result = 0
        self.history = []
    
    def add(self, a, b):
        """Add two numbers"""
        result = a + b
        self.result = result
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def subtract(self, a, b):
        """Subtract b from a"""
        result = a - b
        self.result = result
        self.history.append(f"{a} - {b} = {result}")
        return result
    
    def multiply(self, a, b):
        """Multiply two numbers"""
        result = a * b
        self.result = result
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def divide(self, a, b):
        """Divide a by b"""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        result = a / b
        self.result = result
        self.history.append(f"{a} / {b} = {result}")
        return result
    
    def power(self, a, b):
        """Calculate a to the power of b"""
        result = a ** b
        self.result = result
        self.history.append(f"{a} ^ {b} = {result}")
        return result
    
    def clear(self):
        """Clear the calculator"""
        self.result = 0
        self.history.clear()
    
    def get_last_result(self):
        """Get the last calculation result"""
        return self.result
    
    def get_history(self):
        """Get calculation history"""
        return self.history.copy()
