#!/usr/bin/env python3
"""
演示不同日志策略的效果
"""

import os
import subprocess
import sys
from pathlib import Path


def run_with_strategy(strategy_name, description):
    """使用指定策略运行测试"""
    print(f"\n{'='*60}")
    print(f"运行策略: {strategy_name}")
    print(f"描述: {description}")
    print('='*60)
    
    # 设置环境变量
    env = os.environ.copy()
    env['LOG_STRATEGY'] = strategy_name
    
    # 备份原始环境文件
    if not Path("features/environment_original.py").exists():
        if Path("features/environment.py").exists():
            subprocess.run(["cp", "features/environment.py", "features/environment_original.py"])
    
    # 使用优化的环境文件
    subprocess.run(["cp", "features/environment_optimized.py", "features/environment.py"])
    
    # 清理之前的结果
    if Path("allure-results").exists():
        subprocess.run(["rm", "-rf", "allure-results"])
    Path("allure-results").mkdir(exist_ok=True)
    
    # 运行测试
    cmd = [
        "behave", 
        "-f", "allure_behave.formatter:AllureFormatter", 
        "-o", "allure-results",
        "--tags", "@smoke"  # 只运行冒烟测试以节省时间
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, env=env, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 测试执行成功")
    else:
        print("❌ 测试执行失败")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
    
    # 统计生成的附件数量
    allure_files = list(Path("allure-results").glob("*.json"))
    print(f"生成的 Allure 文件数量: {len(allure_files)}")
    
    # 重命名结果目录以保存
    result_dir = f"allure-results-{strategy_name}"
    if Path(result_dir).exists():
        subprocess.run(["rm", "-rf", result_dir])
    subprocess.run(["mv", "allure-results", result_dir])
    
    print(f"结果保存到: {result_dir}")
    return result.returncode == 0


def main():
    """主函数"""
    print("日志策略演示程序")
    print("这个程序将演示不同的日志策略如何影响 Allure 报告中的附件数量")
    
    strategies = [
        ("buffered", "缓冲策略 - 每个场景一个日志附件"),
        ("level_grouped", "级别分组策略 - 按日志级别分组附件"),
        ("step_grouped", "步骤分组策略 - 按测试步骤分组附件"),
        ("threshold", "阈值策略 - 只附加重要日志和失败场景的所有日志"),
        ("summary", "摘要策略 - 只附加日志统计摘要")
    ]
    
    print("\n可用的策略:")
    for i, (name, desc) in enumerate(strategies, 1):
        print(f"{i}. {name}: {desc}")
    
    print("\n选择要演示的策略:")
    print("0. 演示所有策略")
    for i, (name, desc) in enumerate(strategies, 1):
        print(f"{i}. {name}")
    print("q. 退出")
    
    while True:
        choice = input("\n请输入选择 (0-5, q): ").strip().lower()
        
        if choice == 'q':
            print("再见!")
            break
        elif choice == '0':
            print("\n开始演示所有策略...")
            success_count = 0
            for name, desc in strategies:
                if run_with_strategy(name, desc):
                    success_count += 1
            
            print(f"\n{'='*60}")
            print(f"演示完成! 成功执行 {success_count}/{len(strategies)} 个策略")
            print("\n结果目录:")
            for name, _ in strategies:
                result_dir = f"allure-results-{name}"
                if Path(result_dir).exists():
                    print(f"  - {result_dir}")
            
            print("\n要查看报告，请运行:")
            for name, _ in strategies:
                result_dir = f"allure-results-{name}"
                if Path(result_dir).exists():
                    print(f"  allure serve {result_dir}")
            break
            
        elif choice.isdigit() and 1 <= int(choice) <= len(strategies):
            idx = int(choice) - 1
            name, desc = strategies[idx]
            run_with_strategy(name, desc)
            
            result_dir = f"allure-results-{name}"
            if Path(result_dir).exists():
                print(f"\n要查看报告，请运行: allure serve {result_dir}")
        else:
            print("无效选择，请重新输入")
    
    # 恢复原始环境文件
    if Path("features/environment_original.py").exists():
        subprocess.run(["mv", "features/environment_original.py", "features/environment.py"])
        print("\n已恢复原始环境配置")


if __name__ == "__main__":
    main()
