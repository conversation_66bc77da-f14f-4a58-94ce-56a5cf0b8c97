{"uid": "b1a8273437954620fa374b796ffaacdd", "name": "behaviors", "children": [{"name": "Calculator Operations", "children": [{"name": "Adding two positive numbers", "uid": "91e20027e7599b8c", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677332, "stop": 1750910677341, "duration": 9}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.1 ", "uid": "7cca3edc3e06528e", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677341, "stop": 1750910677347, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["10", "30", "20"], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.2 ", "uid": "f429e489a2304685", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677348, "stop": 1750910677354, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["5", "20", "15"], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.3 ", "uid": "185df6d2648880bf", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677355, "stop": 1750910677360, "duration": 5}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["0", "100", "100"], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.4 ", "uid": "59f1dca969ac1a24", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677361, "stop": 1750910677368, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["-10", "-5", "5"], "tags": ["smoke", "addition"]}, {"name": "Subtracting two numbers", "uid": "f86cef2c28071ea0", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677370, "stop": 1750910677378, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["subtraction"]}, {"name": "Multiplying two numbers", "uid": "4ec9d92406fb8947", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677379, "stop": 1750910677389, "duration": 10}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["multiplication"]}, {"name": "Dividing two numbers", "uid": "495508224e205545", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677391, "stop": 1750910677397, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["division"]}, {"name": "Dividing by zero should raise an error", "uid": "b2b5bf1ee28f1e51", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677399, "stop": 1750910677404, "duration": 5}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["division", "error"]}, {"name": "Calculating power", "uid": "14adb75913a823c9", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "passed", "time": {"start": 1750910677405, "stop": 1750910677412, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["power"]}, {"name": "Calculator maintains calculation history", "uid": "fd2bcf8d45777206", "parentUid": "62873946411c70dd127bd62bc05dae4f", "status": "broken", "time": {"start": 1750910677413, "stop": 1750910677421, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["history"]}], "uid": "62873946411c70dd127bd62bc05dae4f"}, {"name": "计算器功能", "children": [{"name": "两个正数相加", "uid": "f74367bc12b76ad7", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588912, "stop": 1750920588914, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke", "addition"]}, {"name": "不同数字的加法运算 -- @1.1 ", "uid": "8d201361f7ccb23a", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588916, "stop": 1750920588917, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["10", "20", "30"], "tags": ["smoke", "addition"]}, {"name": "不同数字的加法运算 -- @1.2 ", "uid": "9ce932052511d418", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588918, "stop": 1750920588919, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["5", "15", "20"], "tags": ["smoke", "addition"]}, {"name": "不同数字的加法运算 -- @1.3 ", "uid": "d1391713c09029b7", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588920, "stop": 1750920588921, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["0", "100", "100"], "tags": ["smoke", "addition"]}, {"name": "不同数字的加法运算 -- @1.4 ", "uid": "42a39a8305ccb4b2", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588922, "stop": 1750920588923, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["-10", "5", "-5"], "tags": ["smoke", "addition"]}, {"name": "两个数字相减", "uid": "a5072962e2b44053", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588923, "stop": 1750920588924, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["subtraction"]}, {"name": "两个数字相乘", "uid": "4966045406a6a1da", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588925, "stop": 1750920588926, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["multiplication"]}, {"name": "两个数字相除", "uid": "75ef47dcb354da66", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588926, "stop": 1750920588927, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["division"]}, {"name": "除零应该抛出错误", "uid": "5930e130995d950f", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588928, "stop": 1750920588929, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["division", "error"]}, {"name": "计算幂运算", "uid": "cda892081b79743d", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588930, "stop": 1750920588930, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["power"]}, {"name": "计算器维护计算历史记录", "uid": "2627858d6a1f64da", "parentUid": "0df7251ca381de83883f707179569959", "status": "skipped", "time": {"start": 1750920588933, "stop": 1750920588935, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["history"]}, {"name": "测试报告书写", "uid": "e0c58fbcf63a2b40", "parentUid": "0df7251ca381de83883f707179569959", "status": "passed", "time": {"start": 1750920588937, "stop": 1750920588941, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["test"]}], "uid": "0df7251ca381de83883f707179569959"}]}