{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Test defects", "children": [{"name": "\nYou can implement step definitions for undefined steps with these snippets:\n\n@when(u'I have entered 3 into the calculator')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I have entered 3 into the calculator')\n\n", "children": [{"name": "Calculator maintains calculation history", "uid": "fd2bcf8d45777206", "parentUid": "2fe881db72af4b0606c6116eec99dc2a", "status": "broken", "time": {"start": 1750910677413, "stop": 1750910677421, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["history"]}], "uid": "2fe881db72af4b0606c6116eec99dc2a"}], "uid": "bdbf199525818fae7a8651db9eafe741"}]}