{"uid": "a0896460bfc8d4e9", "name": "Adding different numbers -- @1.4 ", "fullName": "Calculator Operations: Adding different numbers", "historyId": "7a0a865e0e4954d9fc2804c1a09bdd33", "time": {"start": 1750910440600, "stop": 1750910440609, "duration": 9}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910440602, "stop": 1750910440603, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "9666fb2cea167159", "name": "Setup", "source": "9666fb2cea167159.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered -10 into the calculator", "time": {"start": 1750910440604, "stop": 1750910440604, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "aaa5b65291530ddf", "name": "Input", "source": "aaa5b65291530ddf.txt", "type": "text/plain", "size": 19}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 5 into the calculator", "time": {"start": 1750910440605, "stop": 1750910440606, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "d79fe6900ee099de", "name": "Input", "source": "d79fe6900ee099de.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910440607, "stop": 1750910440608, "duration": 1}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910440607, "stop": 1750910440608, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "829109872745876", "name": "Operation", "source": "829109872745876.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be -5 on the screen", "time": {"start": 1750910440608, "stop": 1750910440609, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals -5", "time": {"start": 1750910440608, "stop": 1750910440609, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "aeaa062d0b2ac6f0", "name": "Result Verification", "source": "aeaa062d0b2ac6f0.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "first", "value": "-10"}, {"name": "result", "value": "-5"}, {"name": "second", "value": "5"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "a0896460bfc8d4e9.json", "parameterValues": ["-10", "-5", "5"]}