{"uid": "22e0ea8186144fcc", "name": "两个数字相除", "fullName": "计算器功能: 两个数字相除", "historyId": "b173ac1221cc8eeeace98d836653d440", "time": {"start": 1750918797878, "stop": 1750918797891, "duration": 13}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797883, "stop": 1750918797884, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "f213c4f4192cb9f6", "name": "Setup", "source": "f213c4f4192cb9f6.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 84 到计算器中", "time": {"start": 1750918797884, "stop": 1750918797885, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "bf2afc0d1b18a06c", "name": "Input", "source": "bf2afc0d1b18a06c.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 12 到计算器中", "time": {"start": 1750918797886, "stop": 1750918797886, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "688d46b689162e52", "name": "Input", "source": "688d46b689162e52.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下除法按钮", "time": {"start": 1750918797887, "stop": 1750918797888, "duration": 1}, "status": "passed", "steps": [{"name": "Performing division", "time": {"start": 1750918797887, "stop": 1750918797888, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "78f7f95c2f80177c", "name": "Operation", "source": "78f7f95c2f80177c.txt", "type": "text/plain", "size": 16}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 7", "time": {"start": 1750918797888, "stop": 1750918797890, "duration": 2}, "status": "passed", "steps": [{"name": "Verifying result equals 7", "time": {"start": 1750918797888, "stop": 1750918797890, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "6a0a69eff2b8593", "name": "Result Verification", "source": "6a0a69eff2b8593.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["division"]}, "source": "22e0ea8186144fcc.json", "parameterValues": []}