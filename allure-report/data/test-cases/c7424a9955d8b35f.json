{"uid": "c7424a9955d8b35f", "name": "两个数字相减", "fullName": "计算器功能: 两个数字相减", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "time": {"start": 1750918797851, "stop": 1750918797862, "duration": 11}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797853, "stop": 1750918797856, "duration": 3}, "status": "passed", "steps": [], "attachments": [{"uid": "d720d279da33f3bf", "name": "Setup", "source": "d720d279da33f3bf.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 100 到计算器中", "time": {"start": 1750918797856, "stop": 1750918797858, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "58552bf6db41fff5", "name": "Input", "source": "58552bf6db41fff5.txt", "type": "text/plain", "size": 19}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 30 到计算器中", "time": {"start": 1750918797858, "stop": 1750918797859, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "b5aab20d626ec8a4", "name": "Input", "source": "b5aab20d626ec8a4.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下减法按钮", "time": {"start": 1750918797859, "stop": 1750918797860, "duration": 1}, "status": "passed", "steps": [{"name": "Performing subtraction", "time": {"start": 1750918797860, "stop": 1750918797860, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "17a3ad4bd9ba1f60", "name": "Operation", "source": "17a3ad4bd9ba1f60.txt", "type": "text/plain", "size": 20}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 70", "time": {"start": 1750918797861, "stop": 1750918797862, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 70", "time": {"start": 1750918797861, "stop": 1750918797862, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "30b0daf9124824c", "name": "Result Verification", "source": "30b0daf9124824c.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["subtraction"]}, "source": "c7424a9955d8b35f.json", "parameterValues": []}