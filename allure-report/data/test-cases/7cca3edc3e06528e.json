{"uid": "7cca3edc3e06528e", "name": "Adding different numbers -- @1.1 ", "fullName": "Calculator Operations: Adding different numbers", "historyId": "6a60c83de3fb0bee4a30f60694eec811", "time": {"start": 1750910677341, "stop": 1750910677347, "duration": 6}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677343, "stop": 1750910677343, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "c27ef74cda7f479f", "name": "Setup", "source": "c27ef74cda7f479f.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 10 into the calculator", "time": {"start": 1750910677343, "stop": 1750910677344, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "f6886f269f807851", "name": "Input", "source": "f6886f269f807851.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 20 into the calculator", "time": {"start": 1750910677344, "stop": 1750910677345, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "a03bde6e2fa22414", "name": "Input", "source": "a03bde6e2fa22414.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910677345, "stop": 1750910677345, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910677345, "stop": 1750910677345, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "994777b268588856", "name": "Operation", "source": "994777b268588856.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 30 on the screen", "time": {"start": 1750910677346, "stop": 1750910677347, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 30", "time": {"start": 1750910677346, "stop": 1750910677346, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "ecd3c53b9693da2a", "name": "Result Verification", "source": "ecd3c53b9693da2a.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "first", "value": "10"}, {"name": "result", "value": "30"}, {"name": "second", "value": "20"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "868279dd27882ce3", "status": "passed", "time": {"start": 1750910440571, "stop": 1750910440579, "duration": 8}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "7cca3edc3e06528e.json", "parameterValues": ["10", "30", "20"]}