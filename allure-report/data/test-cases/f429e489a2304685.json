{"uid": "f429e489a2304685", "name": "Adding different numbers -- @1.2 ", "fullName": "Calculator Operations: Adding different numbers", "historyId": "e9366fe59f8c428b5a48f708dab6a760", "time": {"start": 1750910677348, "stop": 1750910677354, "duration": 6}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677349, "stop": 1750910677350, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "cd2083adf9f5f0c6", "name": "Setup", "source": "cd2083adf9f5f0c6.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 5 into the calculator", "time": {"start": 1750910677350, "stop": 1750910677351, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "f1f2cf47c5dcf48a", "name": "Input", "source": "f1f2cf47c5dcf48a.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 15 into the calculator", "time": {"start": 1750910677352, "stop": 1750910677353, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "f333204a1b0e981", "name": "Input", "source": "f333204a1b0e981.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910677353, "stop": 1750910677353, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910677353, "stop": 1750910677353, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "3b6a2ace54faa001", "name": "Operation", "source": "3b6a2ace54faa001.txt", "type": "text/plain", "size": 13}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 20 on the screen", "time": {"start": 1750910677354, "stop": 1750910677354, "duration": 0}, "status": "passed", "steps": [{"name": "Verifying result equals 20", "time": {"start": 1750910677354, "stop": 1750910677354, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "93610929473ef1ff", "name": "Result Verification", "source": "93610929473ef1ff.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "first", "value": "5"}, {"name": "result", "value": "20"}, {"name": "second", "value": "15"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "e14ee45cca076b7c", "status": "passed", "time": {"start": 1750910440580, "stop": 1750910440589, "duration": 9}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "f429e489a2304685.json", "parameterValues": ["5", "20", "15"]}