{"uid": "aac0ddea485e68d9", "name": "不同数字的加法运算 -- @1.2 ", "fullName": "计算器功能: 不同数字的加法运算", "historyId": "467e476b619440f9346e9606f609a30c", "time": {"start": 1750918797815, "stop": 1750918797826, "duration": 11}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797817, "stop": 1750918797818, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "cf0e0069f807543b", "name": "Setup", "source": "cf0e0069f807543b.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 5 到计算器中", "time": {"start": 1750918797818, "stop": 1750918797819, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "70a58465f396696d", "name": "Input", "source": "70a58465f396696d.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 15 到计算器中", "time": {"start": 1750918797819, "stop": 1750918797822, "duration": 3}, "status": "passed", "steps": [], "attachments": [{"uid": "bb1340036e19c6f2", "name": "Input", "source": "bb1340036e19c6f2.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下加法按钮", "time": {"start": 1750918797823, "stop": 1750918797824, "duration": 1}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750918797823, "stop": 1750918797824, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "34d228d11d2d454e", "name": "Operation", "source": "34d228d11d2d454e.txt", "type": "text/plain", "size": 13}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 20", "time": {"start": 1750918797824, "stop": 1750918797826, "duration": 2}, "status": "passed", "steps": [{"name": "Verifying result equals 20", "time": {"start": 1750918797825, "stop": 1750918797826, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "9756c131212d4d8d", "name": "Result Verification", "source": "9756c131212d4d8d.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "第一个数", "value": "5"}, {"name": "第二个数", "value": "15"}, {"name": "结果", "value": "20"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "aac0ddea485e68d9.json", "parameterValues": ["5", "15", "20"]}