{"uid": "307a6cd0d77c94b1", "name": "计算幂运算", "fullName": "计算器功能: 计算幂运算", "historyId": "c68aa1364bc6dcc719a82e99b799f3b0", "time": {"start": 1750918797909, "stop": 1750918797928, "duration": 19}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797910, "stop": 1750918797912, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "d03ce6da0d1450ae", "name": "Setup", "source": "d03ce6da0d1450ae.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 2 到计算器中", "time": {"start": 1750918797912, "stop": 1750918797913, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "64e8410aed8de9f1", "name": "Input", "source": "64e8410aed8de9f1.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 3 到计算器中", "time": {"start": 1750918797914, "stop": 1750918797915, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "5a6ef43638caba91", "name": "Input", "source": "5a6ef43638caba91.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下幂运算按钮", "time": {"start": 1750918797919, "stop": 1750918797925, "duration": 6}, "status": "passed", "steps": [{"name": "Performing power calculation", "time": {"start": 1750918797923, "stop": 1750918797925, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "d1b3f0310ccce4f2", "name": "Operation", "source": "d1b3f0310ccce4f2.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 8", "time": {"start": 1750918797926, "stop": 1750918797928, "duration": 2}, "status": "passed", "steps": [{"name": "Verifying result equals 8", "time": {"start": 1750918797926, "stop": 1750918797928, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "29585c2b6284a2b5", "name": "Result Verification", "source": "29585c2b6284a2b5.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["power"]}, "source": "307a6cd0d77c94b1.json", "parameterValues": []}