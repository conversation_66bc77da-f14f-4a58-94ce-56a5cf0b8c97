{"uid": "4ec9d92406fb8947", "name": "Multiplying two numbers", "fullName": "Calculator Operations: Multiplying two numbers", "historyId": "c8e6447541b43bc24b5e60092c7fefbb", "time": {"start": 1750910677379, "stop": 1750910677389, "duration": 10}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677382, "stop": 1750910677383, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "b256eaed87cec492", "name": "Setup", "source": "b256eaed87cec492.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 6 into the calculator", "time": {"start": 1750910677383, "stop": 1750910677384, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "a71e48562e047bc0", "name": "Input", "source": "a71e48562e047bc0.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 7 into the calculator", "time": {"start": 1750910677384, "stop": 1750910677385, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "ab7d7fc83a8c301a", "name": "Input", "source": "ab7d7fc83a8c301a.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press multiply", "time": {"start": 1750910677386, "stop": 1750910677387, "duration": 1}, "status": "passed", "steps": [{"name": "Performing multiplication", "time": {"start": 1750910677386, "stop": 1750910677387, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "6305e70a638d4afe", "name": "Operation", "source": "6305e70a638d4afe.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 42 on the screen", "time": {"start": 1750910677387, "stop": 1750910677388, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 42", "time": {"start": 1750910677388, "stop": 1750910677388, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "61b04b2bc87730e8", "name": "Result Verification", "source": "61b04b2bc87730e8.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "40266ab4e5a6a0d8", "status": "skipped", "time": {"start": 1750910440613, "stop": 1750910440614, "duration": 1}}], "categories": [], "tags": ["multiplication"]}, "source": "4ec9d92406fb8947.json", "parameterValues": []}