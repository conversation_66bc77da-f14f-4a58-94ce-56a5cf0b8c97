{"uid": "e0c58fbcf63a2b40", "name": "测试报告书写", "fullName": "计算器功能: 测试报告书写", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "time": {"start": 1750920588937, "stop": 1750920588941, "duration": 4}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750920588938, "stop": 1750920588939, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "1bd78fbfa927b031", "name": "Setup", "source": "1bd78fbfa927b031.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given given: 测试111", "time": {"start": 1750920588939, "stop": 1750920588939, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "913364cef4559440", "name": "Input", "source": "913364cef4559440.txt", "type": "text/plain", "size": 9}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When when: 测试111", "time": {"start": 1750920588940, "stop": 1750920588940, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "844a469b2537e355", "name": "Input", "source": "844a469b2537e355.txt", "type": "text/plain", "size": 9}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then then: 测试111", "time": {"start": 1750920588941, "stop": 1750920588941, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "a1cbf542510fc83d", "name": "Input", "source": "a1cbf542510fc83d.txt", "type": "text/plain", "size": 9}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 4, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 4}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["test"]}, "source": "e0c58fbcf63a2b40.json", "parameterValues": []}