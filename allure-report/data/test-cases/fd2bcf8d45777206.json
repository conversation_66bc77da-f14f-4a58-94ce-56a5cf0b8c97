{"uid": "fd2bcf8d45777206", "name": "Calculator maintains calculation history", "fullName": "Calculator Operations: Calculator maintains calculation history", "historyId": "7605ba6a4d6e760087e578640b4a5746", "time": {"start": 1750910677413, "stop": 1750910677421, "duration": 8}, "status": "broken", "statusMessage": "\nYou can implement step definitions for undefined steps with these snippets:\n\n@when(u'I have entered 3 into the calculator')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I have entered 3 into the calculator')\n\n", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "broken", "statusMessage": "\nYou can implement step definitions for undefined steps with these snippets:\n\n@when(u'I have entered 3 into the calculator')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I have entered 3 into the calculator')\n\n", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677415, "stop": 1750910677416, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "93f2cfc160b70a61", "name": "Setup", "source": "93f2cfc160b70a61.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 10 into the calculator", "time": {"start": 1750910677416, "stop": 1750910677417, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "1e73cc809f01076b", "name": "Input", "source": "1e73cc809f01076b.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 5 into the calculator", "time": {"start": 1750910677417, "stop": 1750910677417, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "a27a549c48d3b755", "name": "Input", "source": "a27a549c48d3b755.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910677418, "stop": 1750910677418, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910677418, "stop": 1750910677418, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "f11afc1ad565ef05", "name": "Operation", "source": "f11afc1ad565ef05.txt", "type": "text/plain", "size": 13}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 15 on the screen", "time": {"start": 1750910677419, "stop": 1750910677419, "duration": 0}, "status": "passed", "steps": [{"name": "Verifying result equals 15", "time": {"start": 1750910677419, "stop": 1750910677419, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "119bfeb04ae91b3c", "name": "Result Verification", "source": "119bfeb04ae91b3c.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And the calculation history should contain \"10 + 5 = 15\"", "time": {"start": 1750910677419, "stop": 1750910677420, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying history contains: 10 + 5 = 15", "time": {"start": 1750910677420, "stop": 1750910677420, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "f46a566fa7eccfe6", "name": "History Verification", "source": "f46a566fa7eccfe6.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I have entered 3 into the calculator", "time": {"start": 1750910677420, "stop": 1750910677420, "duration": 0}, "status": "broken", "statusMessage": "\nYou can implement step definitions for undefined steps with these snippets:\n\n@when(u'I have entered 3 into the calculator')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I have entered 3 into the calculator')\n\n", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": true, "attachmentsCount": 0}, {"name": "And I press multiply", "time": {"start": 1750910677421, "stop": 1750910677421, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then the result should be 45 on the screen", "time": {"start": 1750910677421, "stop": 1750910677421, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And the calculation history should contain \"15 * 3 = 45\"", "time": {"start": 1750910677421, "stop": 1750910677421, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 13, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 6}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "1eee238ab889ce64", "status": "skipped", "time": {"start": 1750910440625, "stop": 1750910440627, "duration": 2}}], "categories": [{"name": "Test defects", "matchedStatuses": []}], "tags": ["history"]}, "source": "fd2bcf8d45777206.json", "parameterValues": []}