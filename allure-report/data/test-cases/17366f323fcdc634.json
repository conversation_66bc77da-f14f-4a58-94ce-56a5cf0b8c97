{"uid": "17366f323fcdc634", "name": "除零应该抛出错误", "fullName": "计算器功能: 除零应该抛出错误", "historyId": "038434e4c66ac0b41d9341671e2a4539", "time": {"start": 1750918797892, "stop": 1750918797907, "duration": 15}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797895, "stop": 1750918797896, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "b09b3cfcca5af8e8", "name": "Setup", "source": "b09b3cfcca5af8e8.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 10 到计算器中", "time": {"start": 1750918797897, "stop": 1750918797898, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "f5d1ccfb9b54245f", "name": "Input", "source": "f5d1ccfb9b54245f.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 0 到计算器中", "time": {"start": 1750918797898, "stop": 1750918797899, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "2158249561979520", "name": "Input", "source": "2158249561979520.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下除法按钮", "time": {"start": 1750918797899, "stop": 1750918797901, "duration": 2}, "status": "passed", "steps": [{"name": "Performing division", "time": {"start": 1750918797900, "stop": 1750918797901, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "78799f28a387324e", "name": "Error", "source": "78799f28a387324e.txt", "type": "text/plain", "size": 37}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 我应该看到错误信息 \"Cannot divide by zero\"", "time": {"start": 1750918797902, "stop": 1750918797906, "duration": 4}, "status": "passed", "steps": [{"name": "Verifying error message: Cannot divide by zero", "time": {"start": 1750918797903, "stop": 1750918797906, "duration": 3}, "status": "passed", "steps": [], "attachments": [{"uid": "24596fc97334b3cb", "name": "Error Verification", "source": "24596fc97334b3cb.txt", "type": "text/plain", "size": 45}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["division", "error"]}, "source": "17366f323fcdc634.json", "parameterValues": []}