{"uid": "1eee238ab889ce64", "name": "Calculator maintains calculation history", "fullName": "Calculator Operations: Calculator maintains calculation history", "historyId": "7605ba6a4d6e760087e578640b4a5746", "time": {"start": 1750910440625, "stop": 1750910440627, "duration": 2}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910440626, "stop": 1750910440626, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given I have entered 10 into the calculator", "time": {"start": 1750910440626, "stop": 1750910440626, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And I have entered 5 into the calculator", "time": {"start": 1750910440626, "stop": 1750910440626, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When I press add", "time": {"start": 1750910440626, "stop": 1750910440626, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then the result should be 15 on the screen", "time": {"start": 1750910440626, "stop": 1750910440626, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And the calculation history should contain \"10 + 5 = 15\"", "time": {"start": 1750910440626, "stop": 1750910440627, "duration": 1}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When I have entered 3 into the calculator", "time": {"start": 1750910440627, "stop": 1750910440627, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And I press multiply", "time": {"start": 1750910440627, "stop": 1750910440627, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then the result should be 45 on the screen", "time": {"start": 1750910440627, "stop": 1750910440627, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And the calculation history should contain \"15 * 3 = 45\"", "time": {"start": 1750910440627, "stop": 1750910440627, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 10, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["history"]}, "source": "1eee238ab889ce64.json", "parameterValues": []}