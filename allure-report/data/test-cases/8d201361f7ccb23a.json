{"uid": "8d201361f7ccb23a", "name": "不同数字的加法运算 -- @1.1 ", "fullName": "计算器功能: 不同数字的加法运算", "historyId": "d13f8d4ff0120894e90927ccbaa9a3df", "time": {"start": 1750920588916, "stop": 1750920588917, "duration": 1}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750920588917, "stop": 1750920588917, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given 我已经输入数字 10 到计算器中", "time": {"start": 1750920588917, "stop": 1750920588917, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 我已经输入数字 20 到计算器中", "time": {"start": 1750920588917, "stop": 1750920588917, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When 我按下加法按钮", "time": {"start": 1750920588917, "stop": 1750920588917, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then 屏幕上应该显示结果 30", "time": {"start": 1750920588917, "stop": 1750920588917, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "第一个数", "value": "10"}, {"name": "第二个数", "value": "20"}, {"name": "结果", "value": "30"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "54d54c346c156e5c", "status": "passed", "time": {"start": 1750918797806, "stop": 1750918797813, "duration": 7}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "8d201361f7ccb23a.json", "parameterValues": ["10", "20", "30"]}