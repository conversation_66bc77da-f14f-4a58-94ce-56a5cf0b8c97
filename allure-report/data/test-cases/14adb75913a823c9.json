{"uid": "14adb75913a823c9", "name": "Calculating power", "fullName": "Calculator Operations: Calculating power", "historyId": "1bc50e1c959a567c5fd3b555837e554a", "time": {"start": 1750910677405, "stop": 1750910677412, "duration": 7}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677406, "stop": 1750910677407, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "d0de0f409aea5d42", "name": "Setup", "source": "d0de0f409aea5d42.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 2 into the calculator", "time": {"start": 1750910677407, "stop": 1750910677408, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "807eff61335c3544", "name": "Input", "source": "807eff61335c3544.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 3 into the calculator", "time": {"start": 1750910677408, "stop": 1750910677409, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "f98d0a4c2fb1edb6", "name": "Input", "source": "f98d0a4c2fb1edb6.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press power", "time": {"start": 1750910677409, "stop": 1750910677411, "duration": 2}, "status": "passed", "steps": [{"name": "Performing power calculation", "time": {"start": 1750910677410, "stop": 1750910677410, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "652d0163ec3ac6ef", "name": "Operation", "source": "652d0163ec3ac6ef.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 8 on the screen", "time": {"start": 1750910677411, "stop": 1750910677412, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 8", "time": {"start": 1750910677411, "stop": 1750910677412, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "29b443f0d8bf3b05", "name": "Result Verification", "source": "29b443f0d8bf3b05.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "power"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "830133dcc8019f01", "status": "skipped", "time": {"start": 1750910440621, "stop": 1750910440624, "duration": 3}}], "categories": [], "tags": ["power"]}, "source": "14adb75913a823c9.json", "parameterValues": []}