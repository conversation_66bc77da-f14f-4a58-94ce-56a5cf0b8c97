{"uid": "185df6d2648880bf", "name": "Adding different numbers -- @1.3 ", "fullName": "Calculator Operations: Adding different numbers", "historyId": "0ec47a2af2b5dc4d5425cfbe164d1849", "time": {"start": 1750910677355, "stop": 1750910677360, "duration": 5}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677356, "stop": 1750910677357, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "b95b06c8c88e074d", "name": "Setup", "source": "b95b06c8c88e074d.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 0 into the calculator", "time": {"start": 1750910677357, "stop": 1750910677358, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "3f284f497b3c612b", "name": "Input", "source": "3f284f497b3c612b.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 100 into the calculator", "time": {"start": 1750910677358, "stop": 1750910677358, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "2d6fd12fd1dfc03c", "name": "Input", "source": "2d6fd12fd1dfc03c.txt", "type": "text/plain", "size": 19}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910677359, "stop": 1750910677359, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910677359, "stop": 1750910677359, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "2883d3545df13a22", "name": "Operation", "source": "2883d3545df13a22.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 100 on the screen", "time": {"start": 1750910677360, "stop": 1750910677360, "duration": 0}, "status": "passed", "steps": [{"name": "Verifying result equals 100", "time": {"start": 1750910677360, "stop": 1750910677360, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "bcfcd1485e77549a", "name": "Result Verification", "source": "bcfcd1485e77549a.txt", "type": "text/plain", "size": 26}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "first", "value": "0"}, {"name": "result", "value": "100"}, {"name": "second", "value": "100"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "766f47c1b01aa327", "status": "passed", "time": {"start": 1750910440591, "stop": 1750910440599, "duration": 8}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "185df6d2648880bf.json", "parameterValues": ["0", "100", "100"]}