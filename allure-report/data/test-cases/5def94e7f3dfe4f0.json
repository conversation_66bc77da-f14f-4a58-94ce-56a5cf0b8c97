{"uid": "5def94e7f3dfe4f0", "name": "不同数字的加法运算 -- @1.4 ", "fullName": "计算器功能: 不同数字的加法运算", "historyId": "19a7a37e9a802f6db0bfeca21d41fbf0", "time": {"start": 1750918797839, "stop": 1750918797849, "duration": 10}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797841, "stop": 1750918797842, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "f7a1b7a88426caad", "name": "Setup", "source": "f7a1b7a88426caad.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 -10 到计算器中", "time": {"start": 1750918797842, "stop": 1750918797843, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "510ee6964cdcb82d", "name": "Input", "source": "510ee6964cdcb82d.txt", "type": "text/plain", "size": 19}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 5 到计算器中", "time": {"start": 1750918797844, "stop": 1750918797845, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "b0acd57b585eb7f5", "name": "Input", "source": "b0acd57b585eb7f5.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下加法按钮", "time": {"start": 1750918797845, "stop": 1750918797846, "duration": 1}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750918797845, "stop": 1750918797846, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "2520583ea043d2a8", "name": "Operation", "source": "2520583ea043d2a8.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 -5", "time": {"start": 1750918797847, "stop": 1750918797849, "duration": 2}, "status": "passed", "steps": [{"name": "Verifying result equals -5", "time": {"start": 1750918797847, "stop": 1750918797849, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "6b1015b0e8cfb6a3", "name": "Result Verification", "source": "6b1015b0e8cfb6a3.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "第一个数", "value": "-10"}, {"name": "第二个数", "value": "5"}, {"name": "结果", "value": "-5"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "5def94e7f3dfe4f0.json", "parameterValues": ["-10", "5", "-5"]}