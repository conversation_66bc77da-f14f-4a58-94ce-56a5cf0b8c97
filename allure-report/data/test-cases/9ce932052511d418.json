{"uid": "9ce932052511d418", "name": "不同数字的加法运算 -- @1.2 ", "fullName": "计算器功能: 不同数字的加法运算", "historyId": "467e476b619440f9346e9606f609a30c", "time": {"start": 1750920588918, "stop": 1750920588919, "duration": 1}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750920588919, "stop": 1750920588919, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given 我已经输入数字 5 到计算器中", "time": {"start": 1750920588919, "stop": 1750920588919, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 我已经输入数字 15 到计算器中", "time": {"start": 1750920588919, "stop": 1750920588919, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When 我按下加法按钮", "time": {"start": 1750920588919, "stop": 1750920588919, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then 屏幕上应该显示结果 20", "time": {"start": 1750920588919, "stop": 1750920588919, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "第一个数", "value": "5"}, {"name": "第二个数", "value": "15"}, {"name": "结果", "value": "20"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "aac0ddea485e68d9", "status": "passed", "time": {"start": 1750918797815, "stop": 1750918797826, "duration": 11}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "9ce932052511d418.json", "parameterValues": ["5", "15", "20"]}