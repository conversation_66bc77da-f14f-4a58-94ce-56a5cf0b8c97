{"uid": "24e8b83369178953", "name": "Dividing by zero should raise an error", "fullName": "Calculator Operations: Dividing by zero should raise an error", "historyId": "cdaec262e72d3d3dd03fb3d987cc67cc", "time": {"start": 1750910440618, "stop": 1750910440620, "duration": 2}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910440619, "stop": 1750910440620, "duration": 1}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given I have entered 10 into the calculator", "time": {"start": 1750910440620, "stop": 1750910440620, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And I have entered 0 into the calculator", "time": {"start": 1750910440620, "stop": 1750910440620, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When I press divide", "time": {"start": 1750910440620, "stop": 1750910440620, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then I should see an error message \"Cannot divide by zero\"", "time": {"start": 1750910440620, "stop": 1750910440620, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["division", "error"]}, "source": "24e8b83369178953.json", "parameterValues": []}