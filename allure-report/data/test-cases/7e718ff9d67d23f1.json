{"uid": "7e718ff9d67d23f1", "name": "Adding two positive numbers", "fullName": "Calculator Operations: Adding two positive numbers", "historyId": "355b4250f71164e51ec8529a901e5d7b", "time": {"start": 1750910440557, "stop": 1750910440569, "duration": 12}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910440559, "stop": 1750910440561, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "5d62c6671f09568a", "name": "Setup", "source": "5d62c6671f09568a.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 50 into the calculator", "time": {"start": 1750910440563, "stop": 1750910440564, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "ec53b24dc2476dfb", "name": "Input", "source": "ec53b24dc2476dfb.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 70 into the calculator", "time": {"start": 1750910440565, "stop": 1750910440565, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "d6a920b614f74e12", "name": "Input", "source": "d6a920b614f74e12.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910440566, "stop": 1750910440567, "duration": 1}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910440566, "stop": 1750910440567, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "141d9ab4eec490d9", "name": "Operation", "source": "141d9ab4eec490d9.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 120 on the screen", "time": {"start": 1750910440567, "stop": 1750910440568, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 120", "time": {"start": 1750910440568, "stop": 1750910440568, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "344ef7a1e628ff41", "name": "Result Verification", "source": "344ef7a1e628ff41.txt", "type": "text/plain", "size": 26}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "7e718ff9d67d23f1.json", "parameterValues": []}