{"uid": "f74367bc12b76ad7", "name": "两个正数相加", "fullName": "计算器功能: 两个正数相加", "historyId": "bb528dd71bbfcb703aa589df11393955", "time": {"start": 1750920588912, "stop": 1750920588914, "duration": 2}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750920588914, "stop": 1750920588914, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given 我已经输入数字 50 到计算器中", "time": {"start": 1750920588914, "stop": 1750920588914, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 我已经输入数字 70 到计算器中", "time": {"start": 1750920588914, "stop": 1750920588914, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When 我按下加法按钮", "time": {"start": 1750920588914, "stop": 1750920588914, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then 屏幕上应该显示结果 120", "time": {"start": 1750920588914, "stop": 1750920588914, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "5ad37e31ca589318", "status": "passed", "time": {"start": 1750918797795, "stop": 1750918797805, "duration": 10}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "f74367bc12b76ad7.json", "parameterValues": []}