{"uid": "54d54c346c156e5c", "name": "不同数字的加法运算 -- @1.1 ", "fullName": "计算器功能: 不同数字的加法运算", "historyId": "d13f8d4ff0120894e90927ccbaa9a3df", "time": {"start": 1750918797806, "stop": 1750918797813, "duration": 7}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797807, "stop": 1750918797808, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "8c5399daf0c1c4d0", "name": "Setup", "source": "8c5399daf0c1c4d0.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 10 到计算器中", "time": {"start": 1750918797808, "stop": 1750918797809, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "ed034bf140e1f9a0", "name": "Input", "source": "ed034bf140e1f9a0.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 20 到计算器中", "time": {"start": 1750918797810, "stop": 1750918797810, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "c016e98a528dfc5e", "name": "Input", "source": "c016e98a528dfc5e.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下加法按钮", "time": {"start": 1750918797811, "stop": 1750918797812, "duration": 1}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750918797811, "stop": 1750918797812, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "23d8c12b756d482c", "name": "Operation", "source": "23d8c12b756d482c.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 30", "time": {"start": 1750918797812, "stop": 1750918797813, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 30", "time": {"start": 1750918797812, "stop": 1750918797813, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "4f0b49928c246c1e", "name": "Result Verification", "source": "4f0b49928c246c1e.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "第一个数", "value": "10"}, {"name": "第二个数", "value": "20"}, {"name": "结果", "value": "30"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "54d54c346c156e5c.json", "parameterValues": ["10", "20", "30"]}