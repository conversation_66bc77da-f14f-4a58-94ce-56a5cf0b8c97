{"uid": "868279dd27882ce3", "name": "Adding different numbers -- @1.1 ", "fullName": "Calculator Operations: Adding different numbers", "historyId": "6a60c83de3fb0bee4a30f60694eec811", "time": {"start": 1750910440571, "stop": 1750910440579, "duration": 8}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910440573, "stop": 1750910440574, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "55cf930e7f4985c3", "name": "Setup", "source": "55cf930e7f4985c3.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 10 into the calculator", "time": {"start": 1750910440574, "stop": 1750910440575, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "b283359f76db0878", "name": "Input", "source": "b283359f76db0878.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 20 into the calculator", "time": {"start": 1750910440576, "stop": 1750910440576, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "dc30dc47cdcebadf", "name": "Input", "source": "dc30dc47cdcebadf.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910440577, "stop": 1750910440578, "duration": 1}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910440577, "stop": 1750910440578, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "df2b35c463398442", "name": "Operation", "source": "df2b35c463398442.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 30 on the screen", "time": {"start": 1750910440578, "stop": 1750910440579, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 30", "time": {"start": 1750910440578, "stop": 1750910440579, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "9d58ddd72c781599", "name": "Result Verification", "source": "9d58ddd72c781599.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "first", "value": "10"}, {"name": "result", "value": "30"}, {"name": "second", "value": "20"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "868279dd27882ce3.json", "parameterValues": ["10", "30", "20"]}