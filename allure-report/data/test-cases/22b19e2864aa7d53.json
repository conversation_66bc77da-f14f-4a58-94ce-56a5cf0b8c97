{"uid": "22b19e2864aa7d53", "name": "不同数字的加法运算 -- @1.3 ", "fullName": "计算器功能: 不同数字的加法运算", "historyId": "17d7009b30795a79145207c83df82232", "time": {"start": 1750918797830, "stop": 1750918797838, "duration": 8}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797833, "stop": 1750918797834, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "2413e856b4cefda0", "name": "Setup", "source": "2413e856b4cefda0.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 0 到计算器中", "time": {"start": 1750918797834, "stop": 1750918797835, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "4ac8394a1c8d33ac", "name": "Input", "source": "4ac8394a1c8d33ac.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 100 到计算器中", "time": {"start": 1750918797836, "stop": 1750918797836, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "ba4f48d935c0fd5", "name": "Input", "source": "ba4f48d935c0fd5.txt", "type": "text/plain", "size": 19}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下加法按钮", "time": {"start": 1750918797837, "stop": 1750918797837, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750918797837, "stop": 1750918797837, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "4bfaaf271c89fb60", "name": "Operation", "source": "4bfaaf271c89fb60.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 100", "time": {"start": 1750918797838, "stop": 1750918797838, "duration": 0}, "status": "passed", "steps": [{"name": "Verifying result equals 100", "time": {"start": 1750918797838, "stop": 1750918797838, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "8ed27ad4e33346d8", "name": "Result Verification", "source": "8ed27ad4e33346d8.txt", "type": "text/plain", "size": 26}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "第一个数", "value": "0"}, {"name": "第二个数", "value": "100"}, {"name": "结果", "value": "100"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "22b19e2864aa7d53.json", "parameterValues": ["0", "100", "100"]}