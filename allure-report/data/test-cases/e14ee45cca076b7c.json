{"uid": "e14ee45cca076b7c", "name": "Adding different numbers -- @1.2 ", "fullName": "Calculator Operations: Adding different numbers", "historyId": "e9366fe59f8c428b5a48f708dab6a760", "time": {"start": 1750910440580, "stop": 1750910440589, "duration": 9}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910440582, "stop": 1750910440583, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "ad6369013ae2f692", "name": "Setup", "source": "ad6369013ae2f692.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 5 into the calculator", "time": {"start": 1750910440583, "stop": 1750910440584, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "4c3d79507c50015", "name": "Input", "source": "4c3d79507c50015.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 15 into the calculator", "time": {"start": 1750910440585, "stop": 1750910440586, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "ba9a923860a8a97f", "name": "Input", "source": "ba9a923860a8a97f.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910440587, "stop": 1750910440588, "duration": 1}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910440587, "stop": 1750910440588, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "94b81ba55e09bf69", "name": "Operation", "source": "94b81ba55e09bf69.txt", "type": "text/plain", "size": 13}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 20 on the screen", "time": {"start": 1750910440588, "stop": 1750910440589, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 20", "time": {"start": 1750910440589, "stop": 1750910440589, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "fde3d2a290816592", "name": "Result Verification", "source": "fde3d2a290816592.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "first", "value": "5"}, {"name": "result", "value": "20"}, {"name": "second", "value": "15"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "e14ee45cca076b7c.json", "parameterValues": ["5", "20", "15"]}