{"uid": "40266ab4e5a6a0d8", "name": "Multiplying two numbers", "fullName": "Calculator Operations: Multiplying two numbers", "historyId": "c8e6447541b43bc24b5e60092c7fefbb", "time": {"start": 1750910440613, "stop": 1750910440614, "duration": 1}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910440614, "stop": 1750910440614, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given I have entered 6 into the calculator", "time": {"start": 1750910440614, "stop": 1750910440614, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And I have entered 7 into the calculator", "time": {"start": 1750910440614, "stop": 1750910440614, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When I press multiply", "time": {"start": 1750910440614, "stop": 1750910440614, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then the result should be 42 on the screen", "time": {"start": 1750910440614, "stop": 1750910440614, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["multiplication"]}, "source": "40266ab4e5a6a0d8.json", "parameterValues": []}