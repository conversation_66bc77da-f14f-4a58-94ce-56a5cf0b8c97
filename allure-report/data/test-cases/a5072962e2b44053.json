{"uid": "a5072962e2b44053", "name": "两个数字相减", "fullName": "计算器功能: 两个数字相减", "historyId": "0ffa331ae9c2cfbce2744966bdab6e1e", "time": {"start": 1750920588923, "stop": 1750920588924, "duration": 1}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750920588924, "stop": 1750920588924, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given 我已经输入数字 100 到计算器中", "time": {"start": 1750920588924, "stop": 1750920588924, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 我已经输入数字 30 到计算器中", "time": {"start": 1750920588924, "stop": 1750920588924, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When 我按下减法按钮", "time": {"start": 1750920588924, "stop": 1750920588924, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then 屏幕上应该显示结果 70", "time": {"start": 1750920588924, "stop": 1750920588924, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "c7424a9955d8b35f", "status": "passed", "time": {"start": 1750918797851, "stop": 1750918797862, "duration": 11}}], "categories": [], "tags": ["subtraction"]}, "source": "a5072962e2b44053.json", "parameterValues": []}