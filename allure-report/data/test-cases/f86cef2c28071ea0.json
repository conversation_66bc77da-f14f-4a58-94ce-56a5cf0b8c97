{"uid": "f86cef2c28071ea0", "name": "Subtracting two numbers", "fullName": "Calculator Operations: Subtracting two numbers", "historyId": "6e636c33aa713b30db7ead124d6e13cb", "time": {"start": 1750910677370, "stop": 1750910677378, "duration": 8}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677371, "stop": 1750910677371, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "624886b59fce90fc", "name": "Setup", "source": "624886b59fce90fc.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 100 into the calculator", "time": {"start": 1750910677372, "stop": 1750910677372, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "118a548497a2b20", "name": "Input", "source": "118a548497a2b20.txt", "type": "text/plain", "size": 19}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 30 into the calculator", "time": {"start": 1750910677373, "stop": 1750910677373, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "37da3df004ec1f7d", "name": "Input", "source": "37da3df004ec1f7d.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press subtract", "time": {"start": 1750910677374, "stop": 1750910677375, "duration": 1}, "status": "passed", "steps": [{"name": "Performing subtraction", "time": {"start": 1750910677374, "stop": 1750910677375, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "81b3e1baa7ba8f54", "name": "Operation", "source": "81b3e1baa7ba8f54.txt", "type": "text/plain", "size": 20}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 70 on the screen", "time": {"start": 1750910677376, "stop": 1750910677377, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 70", "time": {"start": 1750910677376, "stop": 1750910677377, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "47609d1dd0a188e5", "name": "Result Verification", "source": "47609d1dd0a188e5.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "subtraction"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "c9a7da087a3a4ac5", "status": "skipped", "time": {"start": 1750910440611, "stop": 1750910440612, "duration": 1}}], "categories": [], "tags": ["subtraction"]}, "source": "f86cef2c28071ea0.json", "parameterValues": []}