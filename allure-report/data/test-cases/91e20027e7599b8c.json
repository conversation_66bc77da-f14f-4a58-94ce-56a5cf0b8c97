{"uid": "91e20027e7599b8c", "name": "Adding two positive numbers", "fullName": "Calculator Operations: Adding two positive numbers", "historyId": "355b4250f71164e51ec8529a901e5d7b", "time": {"start": 1750910677332, "stop": 1750910677341, "duration": 9}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677334, "stop": 1750910677336, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "7c376f5d8e637b62", "name": "Setup", "source": "7c376f5d8e637b62.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 50 into the calculator", "time": {"start": 1750910677337, "stop": 1750910677338, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "7f975f3dfe38dcf", "name": "Input", "source": "7f975f3dfe38dcf.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 70 into the calculator", "time": {"start": 1750910677338, "stop": 1750910677339, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "87af11e72f5a8d68", "name": "Input", "source": "87af11e72f5a8d68.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910677339, "stop": 1750910677339, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910677339, "stop": 1750910677339, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "edf53dd45f03b41b", "name": "Operation", "source": "edf53dd45f03b41b.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 120 on the screen", "time": {"start": 1750910677340, "stop": 1750910677340, "duration": 0}, "status": "passed", "steps": [{"name": "Verifying result equals 120", "time": {"start": 1750910677340, "stop": 1750910677340, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "fd37f9ee071f3be", "name": "Result Verification", "source": "fd37f9ee071f3be.txt", "type": "text/plain", "size": 26}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "7e718ff9d67d23f1", "status": "passed", "time": {"start": 1750910440557, "stop": 1750910440569, "duration": 12}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "91e20027e7599b8c.json", "parameterValues": []}