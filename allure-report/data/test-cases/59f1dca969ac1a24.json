{"uid": "59f1dca969ac1a24", "name": "Adding different numbers -- @1.4 ", "fullName": "Calculator Operations: Adding different numbers", "historyId": "7a0a865e0e4954d9fc2804c1a09bdd33", "time": {"start": 1750910677361, "stop": 1750910677368, "duration": 7}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677362, "stop": 1750910677363, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "36e4c109acfdf96f", "name": "Setup", "source": "36e4c109acfdf96f.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered -10 into the calculator", "time": {"start": 1750910677363, "stop": 1750910677364, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "75f87bf0753d4a60", "name": "Input", "source": "75f87bf0753d4a60.txt", "type": "text/plain", "size": 19}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 5 into the calculator", "time": {"start": 1750910677364, "stop": 1750910677365, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "931c2223e95caa81", "name": "Input", "source": "931c2223e95caa81.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910677366, "stop": 1750910677366, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910677366, "stop": 1750910677366, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "48ba8062575dba5f", "name": "Operation", "source": "48ba8062575dba5f.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be -5 on the screen", "time": {"start": 1750910677367, "stop": 1750910677368, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals -5", "time": {"start": 1750910677367, "stop": 1750910677368, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "fa1a2f090f722fcc", "name": "Result Verification", "source": "fa1a2f090f722fcc.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "first", "value": "-10"}, {"name": "result", "value": "-5"}, {"name": "second", "value": "5"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "a0896460bfc8d4e9", "status": "passed", "time": {"start": 1750910440600, "stop": 1750910440609, "duration": 9}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "59f1dca969ac1a24.json", "parameterValues": ["-10", "-5", "5"]}