{"uid": "c15abbeaf2ef1dff", "name": "计算器维护计算历史记录", "fullName": "计算器功能: 计算器维护计算历史记录", "historyId": "2212440e47c6241ce16f9a6ef923a25b", "time": {"start": 1750918797931, "stop": 1750918797958, "duration": 27}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797933, "stop": 1750918797936, "duration": 3}, "status": "passed", "steps": [], "attachments": [{"uid": "208c7a25ac3ad995", "name": "Setup", "source": "208c7a25ac3ad995.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 10 到计算器中", "time": {"start": 1750918797937, "stop": 1750918797938, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "b2fa431846bc81fe", "name": "Input", "source": "b2fa431846bc81fe.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 5 到计算器中", "time": {"start": 1750918797939, "stop": 1750918797941, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "c00b1a4bf2652e1c", "name": "Input", "source": "c00b1a4bf2652e1c.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下加法按钮", "time": {"start": 1750918797942, "stop": 1750918797945, "duration": 3}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750918797943, "stop": 1750918797945, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "75a9508b59226d56", "name": "Operation", "source": "75a9508b59226d56.txt", "type": "text/plain", "size": 13}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 15", "time": {"start": 1750918797946, "stop": 1750918797947, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 15", "time": {"start": 1750918797946, "stop": 1750918797947, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "54a0695577df46a2", "name": "Result Verification", "source": "54a0695577df46a2.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 计算历史记录应该包含 \"10 + 5 = 15\"", "time": {"start": 1750918797948, "stop": 1750918797949, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying history contains: 10 + 5 = 15", "time": {"start": 1750918797948, "stop": 1750918797949, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "2910303c4c951009", "name": "History Verification", "source": "2910303c4c951009.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我已经输入数字 3 到计算器中", "time": {"start": 1750918797951, "stop": 1750918797953, "duration": 2}, "status": "passed", "steps": [], "attachments": [{"uid": "50117288a6671bc7", "name": "Input", "source": "50117288a6671bc7.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我按下乘法按钮", "time": {"start": 1750918797953, "stop": 1750918797954, "duration": 1}, "status": "passed", "steps": [{"name": "Performing multiplication", "time": {"start": 1750918797954, "stop": 1750918797954, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "c786e505cc5e89e9", "name": "Operation", "source": "c786e505cc5e89e9.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 45", "time": {"start": 1750918797955, "stop": 1750918797956, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 45", "time": {"start": 1750918797955, "stop": 1750918797956, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "1046432c45dcd2c8", "name": "Result Verification", "source": "1046432c45dcd2c8.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 计算历史记录应该包含 \"15 * 3 = 45\"", "time": {"start": 1750918797957, "stop": 1750918797958, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying history contains: 15 * 3 = 45", "time": {"start": 1750918797957, "stop": 1750918797958, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "ef9c2b76d8fe1e1e", "name": "History Verification", "source": "ef9c2b76d8fe1e1e.txt", "type": "text/plain", "size": 39}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 16, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 10}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["history"]}, "source": "c15abbeaf2ef1dff.json", "parameterValues": []}