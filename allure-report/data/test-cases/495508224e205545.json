{"uid": "495508224e205545", "name": "Dividing two numbers", "fullName": "Calculator Operations: Dividing two numbers", "historyId": "36f36f57e718739bc89df3a31bafec65", "time": {"start": 1750910677391, "stop": 1750910677397, "duration": 6}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677392, "stop": 1750910677392, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "63b660c12ce65814", "name": "Setup", "source": "63b660c12ce65814.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 84 into the calculator", "time": {"start": 1750910677393, "stop": 1750910677393, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "fa4a28a4698f14d", "name": "Input", "source": "fa4a28a4698f14d.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 12 into the calculator", "time": {"start": 1750910677394, "stop": 1750910677395, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "38e4954346d75cf7", "name": "Input", "source": "38e4954346d75cf7.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press divide", "time": {"start": 1750910677395, "stop": 1750910677396, "duration": 1}, "status": "passed", "steps": [{"name": "Performing division", "time": {"start": 1750910677396, "stop": 1750910677396, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "8b37ed6433630258", "name": "Operation", "source": "8b37ed6433630258.txt", "type": "text/plain", "size": 16}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 7 on the screen", "time": {"start": 1750910677396, "stop": 1750910677397, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 7", "time": {"start": 1750910677396, "stop": 1750910677397, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "7e626c45be21f9c", "name": "Result Verification", "source": "7e626c45be21f9c.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "29e745c0427f1b07", "status": "skipped", "time": {"start": 1750910440615, "stop": 1750910440616, "duration": 1}}], "categories": [], "tags": ["division"]}, "source": "495508224e205545.json", "parameterValues": []}