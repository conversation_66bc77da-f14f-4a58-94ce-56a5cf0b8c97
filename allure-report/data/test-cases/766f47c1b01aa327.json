{"uid": "766f47c1b01aa327", "name": "Adding different numbers -- @1.3 ", "fullName": "Calculator Operations: Adding different numbers", "historyId": "0ec47a2af2b5dc4d5425cfbe164d1849", "time": {"start": 1750910440591, "stop": 1750910440599, "duration": 8}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910440593, "stop": 1750910440594, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "c2794cd689e5dfd4", "name": "Setup", "source": "c2794cd689e5dfd4.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 0 into the calculator", "time": {"start": 1750910440594, "stop": 1750910440595, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "9ebc30f77b19a16c", "name": "Input", "source": "9ebc30f77b19a16c.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 100 into the calculator", "time": {"start": 1750910440595, "stop": 1750910440596, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "765c677ac598166", "name": "Input", "source": "765c677ac598166.txt", "type": "text/plain", "size": 19}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press add", "time": {"start": 1750910440597, "stop": 1750910440597, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750910440597, "stop": 1750910440597, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "c4a299288b0929f2", "name": "Operation", "source": "c4a299288b0929f2.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then the result should be 100 on the screen", "time": {"start": 1750910440598, "stop": 1750910440598, "duration": 0}, "status": "passed", "steps": [{"name": "Verifying result equals 100", "time": {"start": 1750910440598, "stop": 1750910440598, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "148c108e4ba66a82", "name": "Result Verification", "source": "148c108e4ba66a82.txt", "type": "text/plain", "size": 26}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "first", "value": "0"}, {"name": "result", "value": "100"}, {"name": "second", "value": "100"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "766f47c1b01aa327.json", "parameterValues": ["0", "100", "100"]}