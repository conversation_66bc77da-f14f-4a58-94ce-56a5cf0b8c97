{"uid": "2627858d6a1f64da", "name": "计算器维护计算历史记录", "fullName": "计算器功能: 计算器维护计算历史记录", "historyId": "2212440e47c6241ce16f9a6ef923a25b", "time": {"start": 1750920588933, "stop": 1750920588935, "duration": 2}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750920588934, "stop": 1750920588935, "duration": 1}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given 我已经输入数字 10 到计算器中", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 我已经输入数字 5 到计算器中", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When 我按下加法按钮", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then 屏幕上应该显示结果 15", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 计算历史记录应该包含 \"10 + 5 = 15\"", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When 我已经输入数字 3 到计算器中", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 我按下乘法按钮", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then 屏幕上应该显示结果 45", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 计算历史记录应该包含 \"15 * 3 = 45\"", "time": {"start": 1750920588935, "stop": 1750920588935, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 10, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "history"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "c15abbeaf2ef1dff", "status": "passed", "time": {"start": 1750918797931, "stop": 1750918797958, "duration": 27}}], "categories": [], "tags": ["history"]}, "source": "2627858d6a1f64da.json", "parameterValues": []}