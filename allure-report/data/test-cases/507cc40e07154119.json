{"uid": "507cc40e07154119", "name": "两个数字相乘", "fullName": "计算器功能: 两个数字相乘", "historyId": "f3662b2d33db3b7b0965a67f9fdce563", "time": {"start": 1750918797866, "stop": 1750918797876, "duration": 10}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797867, "stop": 1750918797868, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "8fc9bce898f7c7ec", "name": "Setup", "source": "8fc9bce898f7c7ec.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 6 到计算器中", "time": {"start": 1750918797869, "stop": 1750918797870, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "507113010712f07e", "name": "Input", "source": "507113010712f07e.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 7 到计算器中", "time": {"start": 1750918797870, "stop": 1750918797871, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "cef489a3a63beeef", "name": "Input", "source": "cef489a3a63beeef.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下乘法按钮", "time": {"start": 1750918797872, "stop": 1750918797873, "duration": 1}, "status": "passed", "steps": [{"name": "Performing multiplication", "time": {"start": 1750918797872, "stop": 1750918797873, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "befee42f7b4bb263", "name": "Operation", "source": "befee42f7b4bb263.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 42", "time": {"start": 1750918797874, "stop": 1750918797876, "duration": 2}, "status": "passed", "steps": [{"name": "Verifying result equals 42", "time": {"start": 1750918797875, "stop": 1750918797876, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "cd85cc1d3ab22798", "name": "Result Verification", "source": "cd85cc1d3ab22798.txt", "type": "text/plain", "size": 24}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "multiplication"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["multiplication"]}, "source": "507cc40e07154119.json", "parameterValues": []}