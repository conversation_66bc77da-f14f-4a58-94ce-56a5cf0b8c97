{"uid": "b2b5bf1ee28f1e51", "name": "Dividing by zero should raise an error", "fullName": "Calculator Operations: Dividing by zero should raise an error", "historyId": "cdaec262e72d3d3dd03fb3d987cc67cc", "time": {"start": 1750910677399, "stop": 1750910677404, "duration": 5}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given I have a calculator", "time": {"start": 1750910677400, "stop": 1750910677401, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "e6b2fc05df484164", "name": "Setup", "source": "e6b2fc05df484164.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given I have entered 10 into the calculator", "time": {"start": 1750910677401, "stop": 1750910677401, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "c4322582e087f868", "name": "Input", "source": "c4322582e087f868.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And I have entered 0 into the calculator", "time": {"start": 1750910677402, "stop": 1750910677402, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "3e6550b8138752d3", "name": "Input", "source": "3e6550b8138752d3.txt", "type": "text/plain", "size": 17}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When I press divide", "time": {"start": 1750910677403, "stop": 1750910677403, "duration": 0}, "status": "passed", "steps": [{"name": "Performing division", "time": {"start": 1750910677403, "stop": 1750910677403, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "8380b8c0be14f3e6", "name": "Error", "source": "8380b8c0be14f3e6.txt", "type": "text/plain", "size": 37}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then I should see an error message \"Cannot divide by zero\"", "time": {"start": 1750910677403, "stop": 1750910677404, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying error message: Cannot divide by zero", "time": {"start": 1750910677403, "stop": 1750910677404, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "aadd401c104f27fc", "name": "Error Verification", "source": "aadd401c104f27fc.txt", "type": "text/plain", "size": 45}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "Calculator Operations"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "24e8b83369178953", "status": "skipped", "time": {"start": 1750910440618, "stop": 1750910440620, "duration": 2}}], "categories": [], "tags": ["division", "error"]}, "source": "b2b5bf1ee28f1e51.json", "parameterValues": []}