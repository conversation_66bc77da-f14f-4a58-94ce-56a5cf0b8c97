{"uid": "d1391713c09029b7", "name": "不同数字的加法运算 -- @1.3 ", "fullName": "计算器功能: 不同数字的加法运算", "historyId": "17d7009b30795a79145207c83df82232", "time": {"start": 1750920588920, "stop": 1750920588921, "duration": 1}, "status": "skipped", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "skipped", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750920588921, "stop": 1750920588921, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Given 我已经输入数字 0 到计算器中", "time": {"start": 1750920588921, "stop": 1750920588921, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "And 我已经输入数字 100 到计算器中", "time": {"start": 1750920588921, "stop": 1750920588921, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "When 我按下加法按钮", "time": {"start": 1750920588921, "stop": 1750920588921, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "Then 屏幕上应该显示结果 100", "time": {"start": 1750920588921, "stop": 1750920588921, "duration": 0}, "status": "skipped", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "attachments": [], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "第一个数", "value": "0"}, {"name": "第二个数", "value": "100"}, {"name": "结果", "value": "100"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "22b19e2864aa7d53", "status": "passed", "time": {"start": 1750918797830, "stop": 1750918797838, "duration": 8}}], "categories": [], "tags": ["smoke", "addition"]}, "source": "d1391713c09029b7.json", "parameterValues": ["0", "100", "100"]}