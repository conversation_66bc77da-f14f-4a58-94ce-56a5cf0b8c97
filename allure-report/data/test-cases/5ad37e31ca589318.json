{"uid": "5ad37e31ca589318", "name": "两个正数相加", "fullName": "计算器功能: 两个正数相加", "historyId": "bb528dd71bbfcb703aa589df11393955", "time": {"start": 1750918797795, "stop": 1750918797805, "duration": 10}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Given 我有一个计算器", "time": {"start": 1750918797797, "stop": 1750918797800, "duration": 3}, "status": "passed", "steps": [], "attachments": [{"uid": "826079b80928c86b", "name": "Setup", "source": "826079b80928c86b.txt", "type": "text/plain", "size": 22}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Given 我已经输入数字 50 到计算器中", "time": {"start": 1750918797801, "stop": 1750918797801, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "db24c02a53b8fab6", "name": "Input", "source": "db24c02a53b8fab6.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "And 我已经输入数字 70 到计算器中", "time": {"start": 1750918797802, "stop": 1750918797802, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "498872cb5b36bacf", "name": "Input", "source": "498872cb5b36bacf.txt", "type": "text/plain", "size": 18}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "When 我按下加法按钮", "time": {"start": 1750918797803, "stop": 1750918797803, "duration": 0}, "status": "passed", "steps": [{"name": "Performing addition", "time": {"start": 1750918797803, "stop": 1750918797803, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "33cf6c2b6e0192fd", "name": "Operation", "source": "33cf6c2b6e0192fd.txt", "type": "text/plain", "size": 14}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "Then 屏幕上应该显示结果 120", "time": {"start": 1750918797803, "stop": 1750918797804, "duration": 1}, "status": "passed", "steps": [{"name": "Verifying result equals 120", "time": {"start": 1750918797804, "stop": 1750918797804, "duration": 0}, "status": "passed", "steps": [], "attachments": [{"uid": "33bdb54969eaf3c", "name": "Result Verification", "source": "33bdb54969eaf3c.txt", "type": "text/plain", "size": 26}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 1, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 1}], "attachments": [], "parameters": [], "stepsCount": 7, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [], "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": ["smoke", "addition"]}, "source": "5ad37e31ca589318.json", "parameterValues": []}