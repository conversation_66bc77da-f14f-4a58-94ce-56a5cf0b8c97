{"uid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "name": "timeline", "children": [{"name": "测试报告书写", "uid": "e0c58fbcf63a2b40", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750920588937, "stop": 1750920588941, "duration": 4}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["test"]}, {"name": "不同数字的加法运算 -- @1.2 ", "uid": "aac0ddea485e68d9", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797815, "stop": 1750918797826, "duration": 11}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["5", "15", "20"], "tags": ["smoke", "addition"]}, {"name": "两个数字相乘", "uid": "4966045406a6a1da", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588925, "stop": 1750920588926, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["multiplication"]}, {"name": "不同数字的加法运算 -- @1.2 ", "uid": "9ce932052511d418", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588918, "stop": 1750920588919, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["5", "15", "20"], "tags": ["smoke", "addition"]}, {"name": "Dividing two numbers", "uid": "29e745c0427f1b07", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750910440615, "stop": 1750910440616, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["division"]}, {"name": "Calculating power", "uid": "830133dcc8019f01", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750910440621, "stop": 1750910440624, "duration": 3}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["power"]}, {"name": "计算器维护计算历史记录", "uid": "2627858d6a1f64da", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588933, "stop": 1750920588935, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["history"]}, {"name": "两个数字相乘", "uid": "507cc40e07154119", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797866, "stop": 1750918797876, "duration": 10}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["multiplication"]}, {"name": "两个正数相加", "uid": "f74367bc12b76ad7", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588912, "stop": 1750920588914, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["smoke", "addition"]}, {"name": "两个数字相减", "uid": "c7424a9955d8b35f", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797851, "stop": 1750918797862, "duration": 11}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["subtraction"]}, {"name": "Dividing two numbers", "uid": "495508224e205545", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677391, "stop": 1750910677397, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["division"]}, {"name": "Subtracting two numbers", "uid": "f86cef2c28071ea0", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677370, "stop": 1750910677378, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["subtraction"]}, {"name": "Multiplying two numbers", "uid": "4ec9d92406fb8947", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677379, "stop": 1750910677389, "duration": 10}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["multiplication"]}, {"name": "Calculating power", "uid": "14adb75913a823c9", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677405, "stop": 1750910677412, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["power"]}, {"name": "计算器维护计算历史记录", "uid": "c15abbeaf2ef1dff", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797931, "stop": 1750918797958, "duration": 27}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["history"]}, {"name": "两个数字相减", "uid": "a5072962e2b44053", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588923, "stop": 1750920588924, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["subtraction"]}, {"name": "Adding different numbers -- @1.1 ", "uid": "7cca3edc3e06528e", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677341, "stop": 1750910677347, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["10", "30", "20"], "tags": ["smoke", "addition"]}, {"name": "两个数字相除", "uid": "22e0ea8186144fcc", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797878, "stop": 1750918797891, "duration": 13}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["division"]}, {"name": "两个数字相除", "uid": "75ef47dcb354da66", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588926, "stop": 1750920588927, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["division"]}, {"name": "Adding different numbers -- @1.2 ", "uid": "f429e489a2304685", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677348, "stop": 1750910677354, "duration": 6}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["5", "20", "15"], "tags": ["smoke", "addition"]}, {"name": "Multiplying two numbers", "uid": "40266ab4e5a6a0d8", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750910440613, "stop": 1750910440614, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["multiplication"]}, {"name": "Calculator maintains calculation history", "uid": "fd2bcf8d45777206", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "broken", "time": {"start": 1750910677413, "stop": 1750910677421, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["history"]}, {"name": "除零应该抛出错误", "uid": "17366f323fcdc634", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797892, "stop": 1750918797907, "duration": 15}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["division", "error"]}, {"name": "不同数字的加法运算 -- @1.3 ", "uid": "22b19e2864aa7d53", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797830, "stop": 1750918797838, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["0", "100", "100"], "tags": ["smoke", "addition"]}, {"name": "Adding two positive numbers", "uid": "7e718ff9d67d23f1", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910440557, "stop": 1750910440569, "duration": 12}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "addition"]}, {"name": "不同数字的加法运算 -- @1.3 ", "uid": "d1391713c09029b7", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588920, "stop": 1750920588921, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["0", "100", "100"], "tags": ["smoke", "addition"]}, {"name": "Dividing by zero should raise an error", "uid": "24e8b83369178953", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750910440618, "stop": 1750910440620, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["division", "error"]}, {"name": "Dividing by zero should raise an error", "uid": "b2b5bf1ee28f1e51", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677399, "stop": 1750910677404, "duration": 5}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["division", "error"]}, {"name": "不同数字的加法运算 -- @1.1 ", "uid": "54d54c346c156e5c", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797806, "stop": 1750918797813, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["10", "20", "30"], "tags": ["smoke", "addition"]}, {"name": "不同数字的加法运算 -- @1.4 ", "uid": "42a39a8305ccb4b2", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588922, "stop": 1750920588923, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["-10", "5", "-5"], "tags": ["smoke", "addition"]}, {"name": "计算幂运算", "uid": "cda892081b79743d", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588930, "stop": 1750920588930, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["power"]}, {"name": "Calculator maintains calculation history", "uid": "1eee238ab889ce64", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750910440625, "stop": 1750910440627, "duration": 2}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["history"]}, {"name": "Adding two positive numbers", "uid": "91e20027e7599b8c", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677332, "stop": 1750910677341, "duration": 9}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.1 ", "uid": "868279dd27882ce3", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910440571, "stop": 1750910440579, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["10", "30", "20"], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.4 ", "uid": "a0896460bfc8d4e9", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910440600, "stop": 1750910440609, "duration": 9}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["-10", "-5", "5"], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.3 ", "uid": "185df6d2648880bf", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677355, "stop": 1750910677360, "duration": 5}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["0", "100", "100"], "tags": ["smoke", "addition"]}, {"name": "Subtracting two numbers", "uid": "c9a7da087a3a4ac5", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750910440611, "stop": 1750910440612, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["subtraction"]}, {"name": "不同数字的加法运算 -- @1.4 ", "uid": "5def94e7f3dfe4f0", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797839, "stop": 1750918797849, "duration": 10}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["-10", "5", "-5"], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.2 ", "uid": "e14ee45cca076b7c", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910440580, "stop": 1750910440589, "duration": 9}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["5", "20", "15"], "tags": ["smoke", "addition"]}, {"name": "两个正数相加", "uid": "5ad37e31ca589318", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797795, "stop": 1750918797805, "duration": 10}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "addition"]}, {"name": "计算幂运算", "uid": "307a6cd0d77c94b1", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750918797909, "stop": 1750918797928, "duration": 19}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["power"]}, {"name": "Adding different numbers -- @1.4 ", "uid": "59f1dca969ac1a24", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910677361, "stop": 1750910677368, "duration": 7}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["-10", "-5", "5"], "tags": ["smoke", "addition"]}, {"name": "Adding different numbers -- @1.3 ", "uid": "766f47c1b01aa327", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "passed", "time": {"start": 1750910440591, "stop": 1750910440599, "duration": 8}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["0", "100", "100"], "tags": ["smoke", "addition"]}, {"name": "除零应该抛出错误", "uid": "5930e130995d950f", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588928, "stop": 1750920588929, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": [], "tags": ["division", "error"]}, {"name": "不同数字的加法运算 -- @1.1 ", "uid": "8d201361f7ccb23a", "parentUid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "status": "skipped", "time": {"start": 1750920588916, "stop": 1750920588917, "duration": 1}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["10", "20", "30"], "tags": ["smoke", "addition"]}]}