[{"uid": "d1391713c09029b7", "name": "不同数字的加法运算 -- @1.3 ", "time": {"start": 1750920588920, "stop": 1750920588921, "duration": 1}, "status": "skipped", "severity": "normal"}, {"uid": "4ec9d92406fb8947", "name": "Multiplying two numbers", "time": {"start": 1750910677379, "stop": 1750910677389, "duration": 10}, "status": "passed", "severity": "normal"}, {"uid": "7cca3edc3e06528e", "name": "Adding different numbers -- @1.1 ", "time": {"start": 1750910677341, "stop": 1750910677347, "duration": 6}, "status": "passed", "severity": "normal"}, {"uid": "f86cef2c28071ea0", "name": "Subtracting two numbers", "time": {"start": 1750910677370, "stop": 1750910677378, "duration": 8}, "status": "passed", "severity": "normal"}, {"uid": "fd2bcf8d45777206", "name": "Calculator maintains calculation history", "time": {"start": 1750910677413, "stop": 1750910677421, "duration": 8}, "status": "broken", "severity": "normal"}, {"uid": "9ce932052511d418", "name": "不同数字的加法运算 -- @1.2 ", "time": {"start": 1750920588918, "stop": 1750920588919, "duration": 1}, "status": "skipped", "severity": "normal"}, {"uid": "185df6d2648880bf", "name": "Adding different numbers -- @1.3 ", "time": {"start": 1750910677355, "stop": 1750910677360, "duration": 5}, "status": "passed", "severity": "normal"}, {"uid": "f74367bc12b76ad7", "name": "两个正数相加", "time": {"start": 1750920588912, "stop": 1750920588914, "duration": 2}, "status": "skipped", "severity": "normal"}, {"uid": "495508224e205545", "name": "Dividing two numbers", "time": {"start": 1750910677391, "stop": 1750910677397, "duration": 6}, "status": "passed", "severity": "normal"}, {"uid": "14adb75913a823c9", "name": "Calculating power", "time": {"start": 1750910677405, "stop": 1750910677412, "duration": 7}, "status": "passed", "severity": "normal"}, {"uid": "f429e489a2304685", "name": "Adding different numbers -- @1.2 ", "time": {"start": 1750910677348, "stop": 1750910677354, "duration": 6}, "status": "passed", "severity": "normal"}, {"uid": "91e20027e7599b8c", "name": "Adding two positive numbers", "time": {"start": 1750910677332, "stop": 1750910677341, "duration": 9}, "status": "passed", "severity": "normal"}, {"uid": "59f1dca969ac1a24", "name": "Adding different numbers -- @1.4 ", "time": {"start": 1750910677361, "stop": 1750910677368, "duration": 7}, "status": "passed", "severity": "normal"}, {"uid": "8d201361f7ccb23a", "name": "不同数字的加法运算 -- @1.1 ", "time": {"start": 1750920588916, "stop": 1750920588917, "duration": 1}, "status": "skipped", "severity": "normal"}, {"uid": "e0c58fbcf63a2b40", "name": "测试报告书写", "time": {"start": 1750920588937, "stop": 1750920588941, "duration": 4}, "status": "passed", "severity": "normal"}, {"uid": "4966045406a6a1da", "name": "两个数字相乘", "time": {"start": 1750920588925, "stop": 1750920588926, "duration": 1}, "status": "skipped", "severity": "normal"}, {"uid": "75ef47dcb354da66", "name": "两个数字相除", "time": {"start": 1750920588926, "stop": 1750920588927, "duration": 1}, "status": "skipped", "severity": "normal"}, {"uid": "2627858d6a1f64da", "name": "计算器维护计算历史记录", "time": {"start": 1750920588933, "stop": 1750920588935, "duration": 2}, "status": "skipped", "severity": "normal"}, {"uid": "cda892081b79743d", "name": "计算幂运算", "time": {"start": 1750920588930, "stop": 1750920588930, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "b2b5bf1ee28f1e51", "name": "Dividing by zero should raise an error", "time": {"start": 1750910677399, "stop": 1750910677404, "duration": 5}, "status": "passed", "severity": "normal"}, {"uid": "5930e130995d950f", "name": "除零应该抛出错误", "time": {"start": 1750920588928, "stop": 1750920588929, "duration": 1}, "status": "skipped", "severity": "normal"}, {"uid": "a5072962e2b44053", "name": "两个数字相减", "time": {"start": 1750920588923, "stop": 1750920588924, "duration": 1}, "status": "skipped", "severity": "normal"}, {"uid": "42a39a8305ccb4b2", "name": "不同数字的加法运算 -- @1.4 ", "time": {"start": 1750920588922, "stop": 1750920588923, "duration": 1}, "status": "skipped", "severity": "normal"}]