#!/usr/bin/env python3
"""
Test runner script for behave-allure demo
"""

import os
import sys
import subprocess
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import behave
        print("✓ behave is installed")
    except ImportError:
        print("✗ behave is not installed. Run: pip install behave")
        return False
    
    try:
        import allure_behave
        print("✓ allure-behave is installed")
    except ImportError:
        print("✗ allure-behave is not installed. Run: pip install allure-behave")
        return False
    
    return True


def run_tests(tags=None, format_type="pretty"):
    """Run behave tests with optional tags and format"""
    cmd = ["behave"]
    
    if tags:
        cmd.extend(["--tags", tags])
    
    if format_type == "allure":
        cmd.extend(["-f", "allure_behave.formatter:AllureFormatter", "-o", "allure-results"])
    else:
        cmd.extend(["-f", format_type])
    
    print(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)
    return result.returncode == 0


def main():
    """Main function"""
    print("Behave-Allure Calculator Demo")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\nPlease install missing dependencies first:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    
    # Create allure-results directory
    Path("allure-results").mkdir(exist_ok=True)
    
    print("\nAvailable test options:")
    print("1. Run all tests (pretty format)")
    print("2. Run all tests (allure format)")
    print("3. Run smoke tests only")
    print("4. Run addition tests only")
    print("5. Run error tests only")
    print("6. Exit")
    
    while True:
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == "1":
            print("\nRunning all tests with pretty format...")
            run_tests()
            break
        elif choice == "2":
            print("\nRunning all tests with allure format...")
            success = run_tests(format_type="allure")
            if success:
                print("\nTests completed! Allure results saved to 'allure-results' directory.")
                print("To view the report, install allure CLI and run: allure serve allure-results")
            break
        elif choice == "3":
            print("\nRunning smoke tests only...")
            run_tests(tags="@smoke")
            break
        elif choice == "4":
            print("\nRunning addition tests only...")
            run_tests(tags="@addition")
            break
        elif choice == "5":
            print("\nRunning error tests only...")
            run_tests(tags="@error")
            break
        elif choice == "6":
            print("Goodbye!")
            sys.exit(0)
        else:
            print("Invalid choice. Please enter 1-6.")


if __name__ == "__main__":
    main()
