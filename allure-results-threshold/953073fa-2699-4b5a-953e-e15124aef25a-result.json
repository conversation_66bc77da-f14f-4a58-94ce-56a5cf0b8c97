{"name": "不同数字的加法运算 -- @1.3 ", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937807588, "stop": 1750937807588}, {"name": "Given 我已经输入数字 0 到计算器中", "status": "skipped", "start": 1750937807588, "stop": 1750937807588}, {"name": "And 我已经输入数字 100 到计算器中", "status": "skipped", "start": 1750937807588, "stop": 1750937807588}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750937807588, "stop": 1750937807588}, {"name": "Then 屏幕上应该显示结果 100", "status": "skipped", "start": 1750937807588, "stop": 1750937807588}], "parameters": [{"name": "第一个数", "value": "0"}, {"name": "第二个数", "value": "100"}, {"name": "结果", "value": "100"}], "start": 1750937807587, "stop": 1750937807588, "uuid": "d73e524c-b170-42e3-9d8a-0ec4f49c8bf7", "historyId": "17d7009b30795a79145207c83df82232", "fullName": "计算器功能: 不同数字的加法运算", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}