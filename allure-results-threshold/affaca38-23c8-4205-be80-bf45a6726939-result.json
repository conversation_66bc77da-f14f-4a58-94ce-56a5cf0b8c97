{"name": "除零应该抛出错误", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937807597, "stop": 1750937807597}, {"name": "Given 我已经输入数字 10 到计算器中", "status": "skipped", "start": 1750937807597, "stop": 1750937807597}, {"name": "And 我已经输入数字 0 到计算器中", "status": "skipped", "start": 1750937807597, "stop": 1750937807597}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750937807597, "stop": 1750937807597}, {"name": "Then 我应该看到错误信息 \"Cannot divide by zero\"", "status": "skipped", "start": 1750937807597, "stop": 1750937807597}], "start": 1750937807596, "stop": 1750937807597, "uuid": "08c416ae-b04c-40b1-9595-98b545a32fdd", "historyId": "038434e4c66ac0b41d9341671e2a4539", "fullName": "计算器功能: 除零应该抛出错误", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "tag", "value": "error"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}