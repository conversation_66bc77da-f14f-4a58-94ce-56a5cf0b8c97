{"name": "测试报告书写", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "steps": [{"name": "Given 我有一个计算器", "status": "passed", "attachments": [{"name": "Setup", "source": "e9a917af-8302-4279-802d-4641c62ff12a-attachment.txt", "type": "text/plain"}], "start": 1750937807604, "stop": 1750937807605}, {"name": "Given given: 测试111", "status": "passed", "start": 1750937807606, "stop": 1750937807607}, {"name": "When when: 测试111", "status": "broken", "statusDetails": {"message": "Exception: 测试1111\n", "trace": "  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/model.py\", line 1329, in run\n    match.run(runner.context)\n  File \"/opt/anaconda3/envs/ai-pip-test/lib/python3.10/site-packages/behave/matchers.py\", line 98, in run\n    self.func(context, *args, **kwargs)\n  File \"features/steps/calculator_steps.py\", line 17, in step_when_test\n    raise Exception(\"测试1111\")\n"}, "start": 1750937807607, "stop": 1750937807611}, {"name": "Then then: 测试111", "status": "skipped", "start": 1750937807613, "stop": 1750937807613}], "attachments": [{"name": "All Logs (F<PERSON> Scenario) - 测试报告书写", "source": "19c6d22a-8e39-47b8-abee-9ea7ada5c984-attachment.txt", "type": "text/plain"}], "start": 1750937807603, "stop": 1750937807613, "uuid": "5e49c959-1457-435a-8959-67df4b04d493", "historyId": "ef3dd84fc32194d50859abfbe2d42432", "fullName": "计算器功能: 测试报告书写", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "test"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}