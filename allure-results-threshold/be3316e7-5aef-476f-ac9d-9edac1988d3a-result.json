{"name": "两个数字相除", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937807595, "stop": 1750937807595}, {"name": "Given 我已经输入数字 84 到计算器中", "status": "skipped", "start": 1750937807595, "stop": 1750937807595}, {"name": "And 我已经输入数字 12 到计算器中", "status": "skipped", "start": 1750937807595, "stop": 1750937807595}, {"name": "When 我按下除法按钮", "status": "skipped", "start": 1750937807595, "stop": 1750937807595}, {"name": "Then 屏幕上应该显示结果 7", "status": "skipped", "start": 1750937807595, "stop": 1750937807595}], "start": 1750937807594, "stop": 1750937807595, "uuid": "016fa2e6-3e7e-45f5-beec-4d8922611a69", "historyId": "b173ac1221cc8eeeace98d836653d440", "fullName": "计算器功能: 两个数字相除", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "division"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}