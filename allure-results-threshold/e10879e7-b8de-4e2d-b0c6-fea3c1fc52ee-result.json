{"name": "两个正数相加", "status": "skipped", "steps": [{"name": "Given 我有一个计算器", "status": "skipped", "start": 1750937807580, "stop": 1750937807580}, {"name": "Given 我已经输入数字 50 到计算器中", "status": "skipped", "start": 1750937807580, "stop": 1750937807580}, {"name": "And 我已经输入数字 70 到计算器中", "status": "skipped", "start": 1750937807580, "stop": 1750937807580}, {"name": "When 我按下加法按钮", "status": "skipped", "start": 1750937807580, "stop": 1750937807581}, {"name": "Then 屏幕上应该显示结果 120", "status": "skipped", "start": 1750937807581, "stop": 1750937807581}], "start": 1750937807579, "stop": 1750937807581, "uuid": "27ff44e8-8e66-49be-8dd7-d74a31df0d8e", "historyId": "bb528dd71bbfcb703aa589df11393955", "fullName": "计算器功能: 两个正数相加", "labels": [{"name": "severity", "value": "normal"}, {"name": "tag", "value": "addition"}, {"name": "tag", "value": "smoke"}, {"name": "feature", "value": "计算器功能"}, {"name": "framework", "value": "behave"}, {"name": "language", "value": "cpython3"}]}