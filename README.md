# Behave-Allure Calculator Demo

This project demonstrates how to use behave (BDD testing framework) with Allure reporting for Python.

## Project Structure

```
.
├── calculator.py              # Calculator class being tested
├── features/                  # BDD feature files and step definitions
│   ├── calculator.feature     # Feature file with test scenarios
│   ├── environment.py         # Behave environment configuration
│   └── steps/
│       └── calculator_steps.py # Step definitions
├── behave.ini                 # Behave configuration
├── requirements.txt           # Python dependencies
└── README.md                  # This file
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Install Allure command line tool (optional, for viewing reports):
```bash
# On macOS with Homebrew
brew install allure

# On other systems, download from: https://github.com/allure-framework/allure2/releases
```

## Running Tests

### Basic test execution:
```bash
behave
```

### Run with specific tags:
```bash
behave --tags=@smoke
behave --tags=@addition
behave --tags=@error
```

### Run specific feature:
```bash
behave features/calculator.feature
```

### Generate Allure report:
```bash
# Run tests with allure formatter
behave -f allure_behave.formatter:AllureFormatter -o allure-results

# Generate and open HTML report (requires allure command line tool)
allure serve allure-results
```

## Features Demonstrated

### 1. Basic BDD Structure
- **Feature files**: Written in Gherkin syntax with scenarios
- **Step definitions**: Python implementations of Gherkin steps
- **Background**: Common setup steps for all scenarios

### 2. Behave Features
- **Scenario Outline**: Data-driven testing with examples
- **Tags**: Organizing and filtering tests (@smoke, @addition, etc.)
- **Context**: Sharing data between steps
- **Environment hooks**: Setup and teardown operations

### 3. Allure Integration
- **Rich reporting**: Detailed test execution reports
- **Attachments**: Adding context information to test results
- **Steps**: Hierarchical test step organization
- **Tags and metadata**: Enhanced test categorization

### 4. Test Scenarios
- **Positive tests**: Normal calculator operations
- **Negative tests**: Error handling (division by zero)
- **Data-driven tests**: Multiple input combinations
- **State verification**: Calculator history tracking

## Key Files Explained

### calculator.feature
Contains BDD scenarios written in Gherkin syntax:
- `Feature`: High-level description
- `Background`: Common setup for all scenarios
- `Scenario`: Individual test cases
- `Scenario Outline`: Template for data-driven tests
- `Given/When/Then`: BDD step structure

### calculator_steps.py
Python implementations of Gherkin steps:
- `@given`: Setup steps
- `@when`: Action steps  
- `@then`: Verification steps
- Allure integration with attachments and step organization

### environment.py
Behave hooks for test lifecycle:
- `before_all/after_all`: Suite-level setup/teardown
- `before_feature/after_feature`: Feature-level hooks
- `before_scenario/after_scenario`: Scenario-level hooks
- Allure reporting integration

## Example Output

When you run the tests, you'll see:
1. Console output showing test execution
2. Allure results in `allure-results/` directory
3. HTML report (if using `allure serve`)

The Allure report provides:
- Test execution overview
- Detailed step-by-step execution
- Attachments with context information
- Test categorization by tags
- Execution timeline and statistics
