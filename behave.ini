[behave]
# Behave configuration file

# Output format (commented out allure for now)
# format = allure_behave.formatter:AllureFormatter
# outdir = allure-results

# Console output format
format = pretty
stdout_capture = false
stderr_capture = false

# Logging configuration
logging_level = INFO
logging_format = %(levelname)-8s %(name)-10s %(message)s

# Tags configuration
tags = ~@skip

# Step definitions
paths = features
step_definitions = features/steps

# Show skipped tests
show_skipped = true

# Show multiline text
show_multiline = true

# Show timings
show_timings = true

# Default language
default_language = en
