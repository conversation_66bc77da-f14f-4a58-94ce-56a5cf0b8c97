"""
优化的 Behave 环境配置，使用不同的日志策略来减少 Allure 报告中的日志附件数量
"""

import os
from features.log_strategies import create_log_strategy

# Try to import allure, but make it optional
try:
    import allure
    from allure_commons.types import AttachmentType
    ALLURE_AVAILABLE = True
except ImportError:
    ALLURE_AVAILABLE = False
    # Create dummy allure functions if not available
    class DummyAllure:
        @staticmethod
        def attach(*args, **kwargs):
            pass
    allure = DummyAllure()

    class DummyAttachmentType:
        TEXT = "text"
    AttachmentType = DummyAttachmentType()


# 配置使用的日志策略 (可以通过环境变量配置)
LOG_STRATEGY = os.getenv('LOG_STRATEGY', 'buffered')  # 默认使用缓冲策略


def before_all(context):
    """Setup before all tests"""
    print("Starting Calculator Test Suite")
    print(f"Using log strategy: {LOG_STRATEGY}")
    
    # 配置 loguru 与选择的日志策略集成
    if ALLURE_AVAILABLE:
        from loguru import logger
        
        # 创建日志策略实例
        context.log_strategy = create_log_strategy(LOG_STRATEGY, context)
        context.log_strategy.setup()
        
        # 创建一个发送日志到策略的处理器
        def strategy_sink(message):
            record = message.record
            level = record["level"].name
            msg = message.strip()
            timestamp = record["time"]
            context.log_strategy.log_message(level, msg, timestamp)

        # 添加策略处理器（只处理 INFO 及以上级别）
        logger.add(strategy_sink, level="INFO", format="{message}")
        
        # 存储logger引用以便后续使用
        context.logger = logger


def before_feature(context, feature):
    """Setup before each feature"""
    print(f"Starting feature: {feature.name}")


def before_scenario(context, scenario):
    """Setup before each scenario"""
    print(f"Starting scenario: {scenario.name}")
    context.numbers = []
    context.result = None
    context.error = None
    context.scenario_failed = False
    
    # 如果使用步骤分组策略，设置当前步骤
    if hasattr(context, 'log_strategy') and hasattr(context.log_strategy, 'set_current_step'):
        context.log_strategy.set_current_step("Scenario Setup")


def before_step(context, step):
    """Setup before each step"""
    # 如果使用步骤分组策略，更新当前步骤
    if hasattr(context, 'log_strategy') and hasattr(context.log_strategy, 'set_current_step'):
        context.log_strategy.set_current_step(f"{step.keyword} {step.name}")


def after_step(context, step):
    """Cleanup after each step"""
    # 可以在这里添加步骤级别的日志处理
    pass


def after_scenario(context, scenario):
    """Cleanup after each scenario"""
    # 标记场景是否失败（用于阈值策略）
    context.scenario_failed = (scenario.status == "failed")
    
    if scenario.status == "failed":
        print(f"Scenario failed: {scenario.name}")
    else:
        print(f"Scenario passed: {scenario.name}")
    
    # 使用日志策略输出日志
    if hasattr(context, 'log_strategy'):
        context.log_strategy.flush_logs(scenario.name)


def after_feature(context, feature):
    """Cleanup after each feature"""
    print(f"Finished feature: {feature.name}")


def after_all(context):
    """Cleanup after all tests"""
    print("Calculator Test Suite completed")
    
    # 清理日志策略
    if hasattr(context, 'log_strategy'):
        context.log_strategy.cleanup()
