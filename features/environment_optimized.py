"""
优化的 Behave 环境配置，使用缓冲日志策略来减少 Allure 报告中的日志附件数量
"""

from features.log_strategies import BufferedLogStrategy

# Try to import allure, but make it optional
try:
    import allure
    from allure_commons.types import AttachmentType
    ALLURE_AVAILABLE = True
except ImportError:
    ALLURE_AVAILABLE = False
    # Create dummy allure functions if not available
    class DummyAllure:
        @staticmethod
        def attach(*args, **kwargs):
            pass
    allure = DummyAllure()

    class DummyAttachmentType:
        TEXT = "text"
    AttachmentType = DummyAttachmentType()


def before_all(context):
    """Setup before all tests"""
    print("Starting Calculator Test Suite")
    print("Using buffered log strategy")

    # 配置 loguru 与缓冲日志策略集成
    if ALLURE_AVAILABLE:
        from loguru import logger

        # 创建缓冲日志策略实例
        context.log_strategy = BufferedLogStrategy(context)
        context.log_strategy.setup()

        # 创建一个发送日志到策略的处理器
        def strategy_sink(message):
            record = message.record
            level = record["level"].name
            msg = message.strip()
            timestamp = record["time"]
            context.log_strategy.log_message(level, msg, timestamp)

        # 添加策略处理器（只处理 INFO 及以上级别）
        logger.add(strategy_sink, level="INFO", format="{message}")

        # 存储logger引用以便后续使用
        context.logger = logger


def before_feature(context, feature):
    """Setup before each feature"""
    print(f"Starting feature: {feature.name}")


def before_scenario(context, scenario):
    """Setup before each scenario"""
    print(f"Starting scenario: {scenario.name}")
    context.numbers = []
    context.result = None
    context.error = None


def after_scenario(context, scenario):
    """Cleanup after each scenario"""
    if scenario.status == "failed":
        print(f"Scenario failed: {scenario.name}")
    else:
        print(f"Scenario passed: {scenario.name}")

    # 使用缓冲日志策略输出日志
    if hasattr(context, 'log_strategy'):
        context.log_strategy.flush_logs(scenario.name)


def after_feature(context, feature):
    """Cleanup after each feature"""
    print(f"Finished feature: {feature.name}")


def after_all(context):
    """Cleanup after all tests"""
    print("Calculator Test Suite completed")

    # 清理缓冲日志策略
    if hasattr(context, 'log_strategy'):
        context.log_strategy.cleanup()
