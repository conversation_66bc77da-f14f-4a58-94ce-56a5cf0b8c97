"""
Behave environment configuration for allure reporting
"""



# Try to import allure, but make it optional
try:
    import allure
    from allure_commons.types import AttachmentType
    ALLURE_AVAILABLE = True
except ImportError:
    ALLURE_AVAILABLE = False
    # Create dummy allure functions if not available
    class DummyAllure:
        @staticmethod
        def attach(*args, **kwargs):
            pass
    allure = DummyAllure()

    class DummyAttachmentType:
        TEXT = "text"
    AttachmentType = DummyAttachmentType()


def before_all(context):
    """Setup before all tests"""
    print("Starting Calculator Test Suite")
    # 配置 loguru 与 Allure 集成
    if ALLURE_AVAILABLE:
        from loguru import logger

        # 创建一个发送日志到 Allure 的处理器
        def allure_sink(message):
            record = message.record
            level = record["level"].name
            msg = message.strip()
            allure.attach(
                msg,
                name=f"Log [{level}]",
                attachment_type=AttachmentType.TEXT
            )

        # 添加 Allure 处理器（只处理 INFO 及以上级别）
        logger.add(allure_sink, level="INFO", format="{message}")


def before_feature(context, feature):
    """Setup before each feature"""
    print(f"Starting feature: {feature.name}")


def before_scenario(context, scenario):
    """Setup before each scenario"""
    print(f"Starting scenario: {scenario.name}")
    context.numbers = []
    context.result = None
    context.error = None


def after_scenario(context, scenario):
    """Cleanup after each scenario"""
    if scenario.status == "failed":
        print(f"Scenario failed: {scenario.name}")
    else:
        print(f"Scenario passed: {scenario.name}")


def after_feature(context, feature):
    """Cleanup after each feature"""
    print(f"Finished feature: {feature.name}")


def after_all(context):
    """Cleanup after all tests"""
    print("Calculator Test Suite completed")
