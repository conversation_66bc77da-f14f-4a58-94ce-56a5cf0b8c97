"""
不同的日志存储策略，解决 Allure 报告中日志附件过多的问题
"""

import io
from typing import List, Dict, Any
from datetime import datetime

# Try to import allure, but make it optional
try:
    import allure
    from allure_commons.types import AttachmentType
    ALLURE_AVAILABLE = True
except ImportError:
    ALLURE_AVAILABLE = False
    class DummyAllure:
        @staticmethod
        def attach(*args, **kwargs):
            pass
    allure = DummyAllure()
    
    class DummyAttachmentType:
        TEXT = "text"
        JSON = "json"
    AttachmentType = DummyAttachmentType()


class LogStrategy:
    """日志策略基类"""
    
    def __init__(self, context):
        self.context = context
        
    def setup(self):
        """初始化设置"""
        pass
        
    def log_message(self, level: str, message: str, timestamp: datetime = None):
        """记录日志消息"""
        pass
        
    def flush_logs(self, scenario_name: str = None):
        """输出/清空日志"""
        pass
        
    def cleanup(self):
        """清理资源"""
        pass


class BufferedLogStrategy(LogStrategy):
    """方案1: 缓冲区策略 - 收集所有日志，场景结束时统一附加"""
    
    def setup(self):
        self.context.log_buffer = io.StringIO()
        
    def log_message(self, level: str, message: str, timestamp: datetime = None):
        if timestamp is None:
            timestamp = datetime.now()
        timestamp_str = timestamp.strftime("%H:%M:%S.%f")[:-3]
        self.context.log_buffer.write(f"[{timestamp_str}] {level}: {message}\n")
        
    def flush_logs(self, scenario_name: str = None):
        if ALLURE_AVAILABLE and hasattr(self.context, 'log_buffer'):
            log_content = self.context.log_buffer.getvalue()
            if log_content.strip():
                allure.attach(
                    log_content,
                    name=f"Scenario Logs - {scenario_name or 'Unknown'}",
                    attachment_type=AttachmentType.TEXT
                )
            # 清空缓冲区
            self.context.log_buffer.seek(0)
            self.context.log_buffer.truncate(0)


class LevelGroupedLogStrategy(LogStrategy):
    """方案2: 按级别分组策略 - 不同级别的日志分别收集"""
    
    def setup(self):
        self.context.log_groups = {
            'INFO': [],
            'WARNING': [],
            'ERROR': [],
            'DEBUG': []
        }
        
    def log_message(self, level: str, message: str, timestamp: datetime = None):
        if timestamp is None:
            timestamp = datetime.now()
        timestamp_str = timestamp.strftime("%H:%M:%S.%f")[:-3]
        
        if level in self.context.log_groups:
            self.context.log_groups[level].append(f"[{timestamp_str}] {message}")
        
    def flush_logs(self, scenario_name: str = None):
        if ALLURE_AVAILABLE and hasattr(self.context, 'log_groups'):
            for level, messages in self.context.log_groups.items():
                if messages:
                    log_content = '\n'.join(messages)
                    allure.attach(
                        log_content,
                        name=f"{level} Logs - {scenario_name or 'Unknown'}",
                        attachment_type=AttachmentType.TEXT
                    )
                    # 清空该级别的日志
                    messages.clear()


class StepGroupedLogStrategy(LogStrategy):
    """方案3: 按步骤分组策略 - 每个步骤的日志单独收集"""
    
    def setup(self):
        self.context.step_logs = {}
        self.context.current_step = None
        
    def set_current_step(self, step_name: str):
        """设置当前步骤名称"""
        self.context.current_step = step_name
        if step_name not in self.context.step_logs:
            self.context.step_logs[step_name] = []
            
    def log_message(self, level: str, message: str, timestamp: datetime = None):
        if timestamp is None:
            timestamp = datetime.now()
        timestamp_str = timestamp.strftime("%H:%M:%S.%f")[:-3]
        
        step_name = getattr(self.context, 'current_step', 'Unknown Step')
        if step_name not in self.context.step_logs:
            self.context.step_logs[step_name] = []
            
        self.context.step_logs[step_name].append(f"[{timestamp_str}] {level}: {message}")
        
    def flush_logs(self, scenario_name: str = None):
        if ALLURE_AVAILABLE and hasattr(self.context, 'step_logs'):
            for step_name, messages in self.context.step_logs.items():
                if messages:
                    log_content = '\n'.join(messages)
                    allure.attach(
                        log_content,
                        name=f"Step Logs - {step_name}",
                        attachment_type=AttachmentType.TEXT
                    )
            # 清空所有步骤日志
            self.context.step_logs.clear()


class ThresholdLogStrategy(LogStrategy):
    """方案4: 阈值策略 - 只有重要日志或错误时才附加"""
    
    def setup(self):
        self.context.important_logs = []
        self.context.all_logs = []
        self.important_levels = {'WARNING', 'ERROR', 'CRITICAL'}
        
    def log_message(self, level: str, message: str, timestamp: datetime = None):
        if timestamp is None:
            timestamp = datetime.now()
        timestamp_str = timestamp.strftime("%H:%M:%S.%f")[:-3]
        
        log_entry = f"[{timestamp_str}] {level}: {message}"
        self.context.all_logs.append(log_entry)
        
        if level in self.important_levels:
            self.context.important_logs.append(log_entry)
            
    def flush_logs(self, scenario_name: str = None):
        if ALLURE_AVAILABLE:
            # 总是附加重要日志
            if hasattr(self.context, 'important_logs') and self.context.important_logs:
                important_content = '\n'.join(self.context.important_logs)
                allure.attach(
                    important_content,
                    name=f"Important Logs - {scenario_name or 'Unknown'}",
                    attachment_type=AttachmentType.TEXT
                )
            
            # 如果场景失败，附加所有日志
            if hasattr(self.context, 'scenario_failed') and self.context.scenario_failed:
                if hasattr(self.context, 'all_logs') and self.context.all_logs:
                    all_content = '\n'.join(self.context.all_logs)
                    allure.attach(
                        all_content,
                        name=f"All Logs (Failed Scenario) - {scenario_name or 'Unknown'}",
                        attachment_type=AttachmentType.TEXT
                    )
            
            # 清空日志
            if hasattr(self.context, 'important_logs'):
                self.context.important_logs.clear()
            if hasattr(self.context, 'all_logs'):
                self.context.all_logs.clear()


class SummaryLogStrategy(LogStrategy):
    """方案5: 摘要策略 - 只附加日志统计摘要"""
    
    def setup(self):
        self.context.log_stats = {
            'INFO': 0,
            'WARNING': 0,
            'ERROR': 0,
            'DEBUG': 0
        }
        self.context.sample_logs = {
            'INFO': [],
            'WARNING': [],
            'ERROR': [],
            'DEBUG': []
        }
        self.max_samples = 3  # 每个级别最多保留3条示例
        
    def log_message(self, level: str, message: str, timestamp: datetime = None):
        if level in self.context.log_stats:
            self.context.log_stats[level] += 1
            
            # 保留前几条作为示例
            if len(self.context.sample_logs[level]) < self.max_samples:
                if timestamp is None:
                    timestamp = datetime.now()
                timestamp_str = timestamp.strftime("%H:%M:%S.%f")[:-3]
                self.context.sample_logs[level].append(f"[{timestamp_str}] {message}")
                
    def flush_logs(self, scenario_name: str = None):
        if ALLURE_AVAILABLE:
            # 创建摘要报告
            summary_lines = [f"Log Summary for {scenario_name or 'Unknown'}:", "=" * 50]
            
            for level, count in self.context.log_stats.items():
                if count > 0:
                    summary_lines.append(f"{level}: {count} messages")
                    
                    # 添加示例
                    if self.context.sample_logs[level]:
                        summary_lines.append(f"  Sample {level} messages:")
                        for sample in self.context.sample_logs[level]:
                            summary_lines.append(f"    {sample}")
                        if count > len(self.context.sample_logs[level]):
                            summary_lines.append(f"    ... and {count - len(self.context.sample_logs[level])} more")
                        summary_lines.append("")
            
            if any(count > 0 for count in self.context.log_stats.values()):
                summary_content = '\n'.join(summary_lines)
                allure.attach(
                    summary_content,
                    name=f"Log Summary - {scenario_name or 'Unknown'}",
                    attachment_type=AttachmentType.TEXT
                )
            
            # 重置统计
            for level in self.context.log_stats:
                self.context.log_stats[level] = 0
                self.context.sample_logs[level].clear()


# 策略工厂
LOG_STRATEGIES = {
    'buffered': BufferedLogStrategy,
    'level_grouped': LevelGroupedLogStrategy,
    'step_grouped': StepGroupedLogStrategy,
    'threshold': ThresholdLogStrategy,
    'summary': SummaryLogStrategy
}


def create_log_strategy(strategy_name: str, context) -> LogStrategy:
    """创建日志策略实例"""
    if strategy_name not in LOG_STRATEGIES:
        raise ValueError(f"Unknown strategy: {strategy_name}. Available: {list(LOG_STRATEGIES.keys())}")
    
    return LOG_STRATEGIES[strategy_name](context)
