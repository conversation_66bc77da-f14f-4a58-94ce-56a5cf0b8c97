"""
Step definitions for calculator feature tests
"""

from behave import given, when, then, step
from calculator import Calculator
from loguru import logger



@when('when: 测试111')
def step_when_test(context):
    """测试111"""
    logger.info("测试111111")
    logger.info("测试111111")
    logger.info("测试111111")
    logger.info("测试111111")
    logger.info("测试111111")
    pass

@given('given: 测试111')
def step_when_test(context):
    """测试111"""
    logger.info("测试111111")
    logger.info("测试111111")
    logger.info("测试111111")
    logger.info("测试111111")
    pass

@then('then: 测试111')
def step_when_test(context):
    """测试111"""
    logger.info("测试111111")
    logger.info("测试111111")
    logger.info("测试111111")
    logger.info("测试111111")
    logger.info("测试111111")
    pass

# Try to import allure, but make it optional
try:
    import allure
    ALLURE_AVAILABLE = True
except ImportError:
    ALLURE_AVAILABLE = False
    # Create dummy allure functions if not available
    class DummyAllure:
        @staticmethod
        def attach(*args, **kwargs):
            pass
        @staticmethod
        def step(description):
            def decorator(func):
                return func
            return decorator

        class attachment_type:
            TEXT = "text"
    allure = DummyAllure()


@given('I have a calculator')
@given('我有一个计算器')
def step_given_calculator(context):
    """Initialize a calculator instance"""
    context.calculator = Calculator()
    allure.attach("Calculator initialized", name="Setup", attachment_type=allure.attachment_type.TEXT)


@given('I have entered {number:d} into the calculator')
@given('我已经输入数字 {number:d} 到计算器中')
def step_given_number_entered(context, number):
    """Store a number for calculation"""
    if not hasattr(context, 'numbers'):
        context.numbers = []
    context.numbers.append(number)
    allure.attach(f"Number entered: {number}", name="Input", attachment_type=allure.attachment_type.TEXT)


@when('I press add')
@when('我按下加法按钮')
def step_when_press_add(context):
    """Perform addition operation"""
    with allure.step("Performing addition"):
        a, b = context.numbers[-2], context.numbers[-1]
        context.result = context.calculator.add(a, b)
        allure.attach(f"Adding {a} + {b}", name="Operation", attachment_type=allure.attachment_type.TEXT)


@when('I press subtract')
@when('我按下减法按钮')
def step_when_press_subtract(context):
    """Perform subtraction operation"""
    with allure.step("Performing subtraction"):
        a, b = context.numbers[-2], context.numbers[-1]
        context.result = context.calculator.subtract(a, b)
        allure.attach(f"Subtracting {a} - {b}", name="Operation", attachment_type=allure.attachment_type.TEXT)


@when('I press multiply')
@when('我按下乘法按钮')
def step_when_press_multiply(context):
    """Perform multiplication operation"""
    with allure.step("Performing multiplication"):
        if hasattr(context, 'result') and context.result is not None:
            # Use previous result as first operand
            a = context.result
            b = context.numbers[-1]
        else:
            a, b = context.numbers[-2], context.numbers[-1]
        context.result = context.calculator.multiply(a, b)
        allure.attach(f"Multiplying {a} * {b}", name="Operation", attachment_type=allure.attachment_type.TEXT)


@when('I press divide')
@when('我按下除法按钮')
def step_when_press_divide(context):
    """Perform division operation"""
    with allure.step("Performing division"):
        try:
            a, b = context.numbers[-2], context.numbers[-1]
            context.result = context.calculator.divide(a, b)
            allure.attach(f"Dividing {a} / {b}", name="Operation", attachment_type=allure.attachment_type.TEXT)
        except ValueError as e:
            context.error = str(e)
            allure.attach(f"Division error: {str(e)}", name="Error", attachment_type=allure.attachment_type.TEXT)


@when('I press power')
@when('我按下幂运算按钮')
def step_when_press_power(context):
    """Perform power operation"""
    with allure.step("Performing power calculation"):
        a, b = context.numbers[-2], context.numbers[-1]
        context.result = context.calculator.power(a, b)
        allure.attach(f"Calculating {a} ^ {b}", name="Operation", attachment_type=allure.attachment_type.TEXT)


@then('the result should be {expected:d} on the screen')
@then('屏幕上应该显示结果 {expected:d}')
def step_then_result_should_be(context, expected):
    """Verify the calculation result"""
    with allure.step(f"Verifying result equals {expected}"):
        actual = context.result
        assert actual == expected, f"Expected {expected}, but got {actual}"
        allure.attach(f"Expected: {expected}, Actual: {actual}", name="Result Verification",
                     attachment_type=allure.attachment_type.TEXT)


@then('I should see an error message "{message}"')
@then('我应该看到错误信息 "{message}"')
def step_then_error_message(context, message):
    """Verify error message"""
    with allure.step(f"Verifying error message: {message}"):
        assert hasattr(context, 'error'), "No error was raised"
        assert context.error == message, f"Expected '{message}', but got '{context.error}'"
        allure.attach(f"Error message verified: {message}", name="Error Verification",
                     attachment_type=allure.attachment_type.TEXT)


@then('the calculation history should contain "{expected_entry}"')
@then('计算历史记录应该包含 "{expected_entry}"')
def step_then_history_contains(context, expected_entry):
    """Verify calculation history contains expected entry"""
    with allure.step(f"Verifying history contains: {expected_entry}"):
        history = context.calculator.get_history()
        assert expected_entry in history, f"Expected '{expected_entry}' in history, but got {history}"
        allure.attach(f"History: {history}", name="History Verification",
                     attachment_type=allure.attachment_type.TEXT)


@when('I have entered {number:d} into the calculator')
@when('我已经输入数字 {number:d} 到计算器中')
def step_when_number_entered(context, number):
    """Store a number for calculation (when step)"""
    if not hasattr(context, 'numbers'):
        context.numbers = []
    context.numbers.append(number)
    allure.attach(f"Number entered: {number}", name="Input", attachment_type=allure.attachment_type.TEXT)


