"""
Behave environment configuration for allure reporting
"""



# Try to import allure, but make it optional
try:
    import allure
    from allure_commons.types import AttachmentType
    ALLURE_AVAILABLE = True
except ImportError:
    ALLURE_AVAILABLE = False
    # Create dummy allure functions if not available
    class DummyAllure:
        @staticmethod
        def attach(*args, **kwargs):
            pass
    allure = DummyAllure()

    class DummyAttachmentType:
        TEXT = "text"
    AttachmentType = DummyAttachmentType()


def before_all(context):
    """Setup before all tests"""
    print("Starting Calculator Test Suite")
    # 配置 loguru 与 Allure 集成
    if ALLURE_AVAILABLE:
        from loguru import logger
        import io

        # 创建一个内存缓冲区来收集日志
        context.log_buffer = io.StringIO()

        # 创建一个收集日志到缓冲区的处理器
        def buffer_sink(message):
            record = message.record
            level = record["level"].name
            timestamp = record["time"].strftime("%H:%M:%S.%f")[:-3]
            msg = message.strip()
            context.log_buffer.write(f"[{timestamp}] {level}: {msg}\n")

        # 添加缓冲区处理器（只处理 INFO 及以上级别）
        logger.add(buffer_sink, level="INFO", format="{message}")

        # 存储logger引用以便后续使用
        context.logger = logger


def before_feature(context, feature):
    """Setup before each feature"""
    print(f"Starting feature: {feature.name}")


def before_scenario(context, scenario):
    """Setup before each scenario"""
    print(f"Starting scenario: {scenario.name}")
    context.numbers = []
    context.result = None
    context.error = None

    # 清空日志缓冲区，为新场景准备
    if ALLURE_AVAILABLE and hasattr(context, 'log_buffer'):
        context.log_buffer.seek(0)
        context.log_buffer.truncate(0)


def after_scenario(context, scenario):
    """Cleanup after each scenario"""
    if scenario.status == "failed":
        print(f"Scenario failed: {scenario.name}")
    else:
        print(f"Scenario passed: {scenario.name}")

    # 在场景结束时，将所有收集的日志作为一个附件添加到 Allure
    if ALLURE_AVAILABLE and hasattr(context, 'log_buffer'):
        log_content = context.log_buffer.getvalue()
        if log_content.strip():  # 只有当有日志内容时才添加附件
            allure.attach(
                log_content,
                name=f"Scenario Logs - {scenario.name}",
                attachment_type=AttachmentType.TEXT
            )


def after_feature(context, feature):
    """Cleanup after each feature"""
    print(f"Finished feature: {feature.name}")


def after_all(context):
    """Cleanup after all tests"""
    print("Calculator Test Suite completed")
