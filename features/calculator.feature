Feature: 计算器功能
  作为一个用户
  我想要执行基本的数学运算
  以便我能够准确地计算结果

  Background:
    Given 我有一个计算器

  @smoke @addition
  Scenario: 两个正数相加
    Given 我已经输入数字 50 到计算器中
    And 我已经输入数字 70 到计算器中
    When 我按下加法按钮
    Then 屏幕上应该显示结果 120

  @smoke @addition
  Scenario Outline: 不同数字的加法运算
    Given 我已经输入数字 <第一个数> 到计算器中
    And 我已经输入数字 <第二个数> 到计算器中
    When 我按下加法按钮
    Then 屏幕上应该显示结果 <结果>

    Examples:
      | 第一个数 | 第二个数 | 结果 |
      | 10      | 20      | 30  |
      | 5       | 15      | 20  |
      | 0       | 100     | 100 |
      | -10     | 5       | -5  |

  @subtraction
  Scenario: 两个数字相减
    Given 我已经输入数字 100 到计算器中
    And 我已经输入数字 30 到计算器中
    When 我按下减法按钮
    Then 屏幕上应该显示结果 70

  @multiplication
  Scenario: 两个数字相乘
    Given 我已经输入数字 6 到计算器中
    And 我已经输入数字 7 到计算器中
    When 我按下乘法按钮
    Then 屏幕上应该显示结果 42

  @division
  Scenario: 两个数字相除
    Given 我已经输入数字 84 到计算器中
    And 我已经输入数字 12 到计算器中
    When 我按下除法按钮
    Then 屏幕上应该显示结果 7

  @division @error
  Scenario: 除零应该抛出错误
    Given 我已经输入数字 10 到计算器中
    And 我已经输入数字 0 到计算器中
    When 我按下除法按钮
    Then 我应该看到错误信息 "Cannot divide by zero"

  @power
  Scenario: 计算幂运算
    Given 我已经输入数字 2 到计算器中
    And 我已经输入数字 3 到计算器中
    When 我按下幂运算按钮
    Then 屏幕上应该显示结果 8

  @history
  Scenario: 计算器维护计算历史记录
    Given 我已经输入数字 10 到计算器中
    And 我已经输入数字 5 到计算器中
    When 我按下加法按钮
    Then 屏幕上应该显示结果 15
    And 计算历史记录应该包含 "10 + 5 = 15"
    When 我已经输入数字 3 到计算器中
    And 我按下乘法按钮
    Then 屏幕上应该显示结果 45
    And 计算历史记录应该包含 "15 * 3 = 45"

    @test
    Scenario: 测试报告书写
      Given given: 测试111
      When when: 测试111
      Then then: 测试111